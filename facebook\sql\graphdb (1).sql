-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Aug 07, 2025 at 10:12 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `graphdb`
--

-- --------------------------------------------------------

--
-- Table structure for table `admin_activity_log`
--

CREATE TABLE `admin_activity_log` (
  `id` int(11) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `admin_users`
--

CREATE TABLE `admin_users` (
  `id` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `email` varchar(100) NOT NULL,
  `password_hash` varchar(255) NOT NULL,
  `role` enum('superadmin','admin','moderator') DEFAULT 'admin',
  `is_active` tinyint(1) DEFAULT 1,
  `2fa_enabled` tinyint(1) DEFAULT 0,
  `2fa_secret` varchar(32) DEFAULT NULL,
  `failed_attempts` int(11) DEFAULT 0,
  `locked_until` timestamp NULL DEFAULT NULL,
  `last_login` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `admin_users`
--

INSERT INTO `admin_users` (`id`, `username`, `email`, `password_hash`, `role`, `is_active`, `2fa_enabled`, `2fa_secret`, `failed_attempts`, `locked_until`, `last_login`, `created_at`, `updated_at`) VALUES
(1, 'admin', '<EMAIL>', '$2y$10$XDvLyY11zXM099BemT6VduEOMnRQdAv4uon4ILT9aSogmILqXrfsi', 'superadmin', 1, 0, NULL, 0, NULL, '2025-08-05 13:31:57', '2025-08-05 08:26:16', '2025-08-05 13:31:57'),
(2, 'testuser', '<EMAIL>', '$2y$10$gAJfW4GaDCfC2I7ZZfsBu.V82JMaohh9GkxrQ0EfE3e9.m79STtIS', 'admin', 1, 0, NULL, 0, NULL, NULL, '2025-08-05 12:23:10', '2025-08-05 12:23:10');

-- --------------------------------------------------------

--
-- Table structure for table `licenses`
--

CREATE TABLE `licenses` (
  `id` int(11) UNSIGNED NOT NULL,
  `license_key` varchar(255) NOT NULL,
  `customer_name` varchar(255) NOT NULL,
  `customer_email` varchar(255) NOT NULL,
  `license_type` enum('basic','professional','enterprise') NOT NULL DEFAULT 'basic',
  `status` enum('active','suspended','expired') DEFAULT 'active',
  `max_devices` int(11) DEFAULT 3,
  `duration_months` int(11) NOT NULL DEFAULT 12,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL,
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `notes` text DEFAULT NULL,
  `created_by` int(11) UNSIGNED DEFAULT NULL,
  `total_searches` int(11) DEFAULT 0,
  `last_used` timestamp NULL DEFAULT NULL,
  `license_data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`license_data`)),
  `devices_used` int(11) DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `licenses`
--

INSERT INTO `licenses` (`id`, `license_key`, `customer_name`, `customer_email`, `license_type`, `status`, `max_devices`, `duration_months`, `created_at`, `expires_at`, `updated_at`, `notes`, `created_by`, `total_searches`, `last_used`, `license_data`, `devices_used`) VALUES
(22, '3E6E-3D2D-3524-726A-7137-2827-2C51-121E-1629-242F-2229-6673-6C21-2D3F-3D2B-2434-393E-152E-3924-5B5C-1C57-2324-6C67-6721-313E-2C3E-3010-2435-3572-6977-7B64-7707-1D02-0D61-7979-6969-662E-2B2B-293B-3531-2434-7269-777B-6477-071D-020C-6179-796B-7572-737A-7476-', 'Joe Doe', '<EMAIL>', 'basic', 'active', 1, 1, '2025-08-07 06:41:28', '2025-09-07 06:41:28', '2025-08-07 06:41:28', '', 1, 0, NULL, '{\"type\":\"basic\",\"email\":\"<EMAIL>\",\"generated_date\":\"2025-08-07 06:41:28\",\"version\":\"2.0\"}', 0),
(23, '3E6E-3D2D-3524-726A-7137-2827-2C51-121E-1629-242F-2229-6673-6C2B-2820-222D-3427-3F62-6470-1422-5F51-5B58-622A-2126-6768-6B2B-3D3C-2026-3C05-3124-3677-7376-7702-0204-197C-7163-7B72-6665-6C22-2927-3137-2024-3537-7773-7677-0202-0719-7C71-637B-7264-7979-7F7D-', 'Godwin Bb', '<EMAIL>', 'basic', 'active', 3, 12, '2025-08-07 07:14:38', '2026-08-07 07:14:38', '2025-08-07 07:14:38', '', 1, 0, NULL, '{\"type\":\"basic\",\"email\":\"<EMAIL>\",\"generated_date\":\"2025-08-07 07:14:38\",\"version\":\"2.0\"}', 0);

-- --------------------------------------------------------

--
-- Table structure for table `login_attempts`
--

CREATE TABLE `login_attempts` (
  `id` int(11) NOT NULL,
  `username` varchar(50) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `success` tinyint(1) NOT NULL,
  `failure_reason` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `login_sessions`
--

CREATE TABLE `login_sessions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `session_id` varchar(128) NOT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `token` varchar(64) NOT NULL,
  `email` varchar(255) NOT NULL,
  `admin_id` int(11) NOT NULL,
  `expires_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `used_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `password_reset_tokens`
--

INSERT INTO `password_reset_tokens` (`id`, `user_id`, `token`, `email`, `admin_id`, `expires_at`, `used`, `created_at`, `used_at`) VALUES
(2, 2, '41307c48211dc681d48b003a977bd37486fee83fdfea7d312297ebdd2b66ae4a', '<EMAIL>', 2, '2025-08-05 11:25:41', 0, '2025-08-05 12:25:41', NULL),
(3, 2, 'e94bbaeedce38045964201c3d98828905ee1f687acb796e7a6319a4c71c87960', '<EMAIL>', 2, '2025-08-05 11:27:56', 0, '2025-08-05 12:27:56', NULL),
(4, 2, 'ad15087751b329d56d3dc02bb411b67686c710f261b475ffacf3b5180999ffd4', '<EMAIL>', 2, '2025-08-05 12:16:15', 0, '2025-08-05 13:16:15', NULL),
(5, 2, '4bb81da38670ecd192cb133bcca67302ec0f5e976cd6776e1a41da7413b0d9db', '<EMAIL>', 2, '2025-08-05 12:19:12', 0, '2025-08-05 13:19:12', NULL),
(6, 2, '881ec19cd955d4dcaea2bd0be2f5248f5e2eabb4128486b80e7737027d2fb9ee', '<EMAIL>', 2, '2025-08-05 12:20:48', 0, '2025-08-05 13:20:48', NULL),
(7, 2, '91b40ee85b981f0c0e431ddf5c02a49163a971ceb1dbf49f31742101e694896e', '<EMAIL>', 2, '2025-08-05 12:21:18', 0, '2025-08-05 13:21:18', NULL),
(8, 2, '63993c7abbe17617738917c1682fd1d5d548c8a7692e4b1693920cbc82a9003e', '<EMAIL>', 2, '2025-08-05 12:21:35', 0, '2025-08-05 13:21:35', NULL),
(9, 2, '6e4572ebaf14446f7de327ceff3f94c35c7783fc2e71a78ca0995d805a412c15', '<EMAIL>', 2, '2025-08-05 12:25:32', 0, '2025-08-05 13:25:32', NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `admin_users`
--
ALTER TABLE `admin_users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- Indexes for table `licenses`
--
ALTER TABLE `licenses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `license_key` (`license_key`),
  ADD KEY `idx_license_key` (`license_key`),
  ADD KEY `idx_customer_email` (`customer_email`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_expires_at` (`expires_at`),
  ADD KEY `idx_created_by` (`created_by`),
  ADD KEY `idx_license_type` (`license_type`);

--
-- Indexes for table `login_attempts`
--
ALTER TABLE `login_attempts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_username` (`username`),
  ADD KEY `idx_ip_address` (`ip_address`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `login_sessions`
--
ALTER TABLE `login_sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `admin_users`
--
ALTER TABLE `admin_users`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `licenses`
--
ALTER TABLE `licenses`
  MODIFY `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=24;

--
-- AUTO_INCREMENT for table `login_attempts`
--
ALTER TABLE `login_attempts`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `login_sessions`
--
ALTER TABLE `login_sessions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `admin_activity_log`
--
ALTER TABLE `admin_activity_log`
  ADD CONSTRAINT `admin_activity_log_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `login_sessions`
--
ALTER TABLE `login_sessions`
  ADD CONSTRAINT `login_sessions_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD CONSTRAINT `password_reset_tokens_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `admin_users` (`id`) ON DELETE CASCADE;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
