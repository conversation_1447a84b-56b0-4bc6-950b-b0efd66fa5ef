<?php
/**
 * Two-Factor Authentication Class
 * 
 * Implements TOTP (Time-based One-Time Password) authentication
 * compatible with Google Authenticator, Authy, and other TOTP apps.
 */

class TwoFactorAuth {
    private $issuer;
    private $window;
    
    public function __construct() {
        $config = getConfig('security');
        $this->issuer = $config['2fa_issuer'];
        $this->window = $config['2fa_window'];
    }
    
    /**
     * Generate a new secret key for 2FA
     */
    public function generateSecret($length = 32) {
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $secret = '';
        
        for ($i = 0; $i < $length; $i++) {
            $secret .= $chars[random_int(0, strlen($chars) - 1)];
        }
        
        return $secret;
    }
    
    /**
     * Generate QR code URL for setting up 2FA
     */
    public function getQRCodeUrl($secret, $username, $issuer = null) {
        $issuer = $issuer ?: $this->issuer;
        $label = urlencode($issuer . ':' . $username);
        $issuer = urlencode($issuer);
        
        $otpauth = "otpauth://totp/{$label}?secret={$secret}&issuer={$issuer}";
        
        // Using Google Charts API for QR code generation
        return "https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=" . urlencode($otpauth);
    }
    
    /**
     * Generate QR code as base64 image (alternative to Google Charts)
     */
    public function getQRCodeBase64($secret, $username, $issuer = null) {
        // This would require a QR code library like endroid/qr-code
        // For now, return the URL for Google Charts
        return $this->getQRCodeUrl($secret, $username, $issuer);
    }
    
    /**
     * Verify TOTP code
     */
    public function verifyCode($secret, $code, $timestamp = null) {
        $timestamp = $timestamp ?: time();
        
        // Remove any spaces or formatting from the code
        $code = preg_replace('/\s+/', '', $code);
        
        if (strlen($code) !== 6 || !ctype_digit($code)) {
            return false;
        }
        
        // Check current time window and adjacent windows
        for ($i = -$this->window; $i <= $this->window; $i++) {
            $timeSlice = intval($timestamp / 30) + $i;
            $calculatedCode = $this->calculateCode($secret, $timeSlice);
            
            if ($this->safeStringCompare($code, $calculatedCode)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get current TOTP code (for testing purposes)
     */
    public function getCurrentCode($secret, $timestamp = null) {
        $timestamp = $timestamp ?: time();
        $timeSlice = intval($timestamp / 30);
        return $this->calculateCode($secret, $timeSlice);
    }
    
    /**
     * Calculate TOTP code for given time slice
     */
    private function calculateCode($secret, $timeSlice) {
        // Decode base32 secret
        $secretKey = $this->base32Decode($secret);
        
        // Pack time slice as 64-bit big-endian
        $time = pack('N*', 0) . pack('N*', $timeSlice);
        
        // Generate HMAC-SHA1 hash
        $hash = hash_hmac('sha1', $time, $secretKey, true);
        
        // Extract dynamic binary code
        $offset = ord($hash[19]) & 0xf;
        $code = (
            ((ord($hash[$offset + 0]) & 0x7f) << 24) |
            ((ord($hash[$offset + 1]) & 0xff) << 16) |
            ((ord($hash[$offset + 2]) & 0xff) << 8) |
            (ord($hash[$offset + 3]) & 0xff)
        ) % 1000000;
        
        return str_pad($code, 6, '0', STR_PAD_LEFT);
    }
    
    /**
     * Decode base32 string
     */
    private function base32Decode($secret) {
        $secret = strtoupper($secret);
        $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
        $output = '';
        $v = 0;
        $vbits = 0;
        
        for ($i = 0; $i < strlen($secret); $i++) {
            $char = $secret[$i];
            $value = strpos($alphabet, $char);
            
            if ($value === false) {
                continue; // Skip invalid characters
            }
            
            $v = ($v << 5) | $value;
            $vbits += 5;
            
            if ($vbits >= 8) {
                $output .= chr(($v >> ($vbits - 8)) & 255);
                $vbits -= 8;
            }
        }
        
        return $output;
    }
    
    /**
     * Safe string comparison to prevent timing attacks
     */
    private function safeStringCompare($known, $user) {
        if (strlen($known) !== strlen($user)) {
            return false;
        }
        
        $result = 0;
        for ($i = 0; $i < strlen($known); $i++) {
            $result |= ord($known[$i]) ^ ord($user[$i]);
        }
        
        return $result === 0;
    }
    
    /**
     * Enable 2FA for a user
     */
    public function enableTwoFactor($userId, $secret, $verificationCode) {
        // Verify the code before enabling
        if (!$this->verifyCode($secret, $verificationCode)) {
            throw new Exception('Invalid verification code. Please try again.');
        }
        
        $db = Database::getInstance();
        
        $result = $db->update('admin_users',
            ['2fa_secret' => $secret, '2fa_enabled' => 1],
            'id = :id',
            [':id' => $userId]
        );
        
        if ($result === 0) {
            throw new Exception('Failed to enable two-factor authentication.');
        }
        
        // Log the 2FA enablement
        $logger = new SecurityLogger();
        $logger->logTwoFactorEnabled($userId, $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
        
        return true;
    }
    
    /**
     * Disable 2FA for a user
     */
    public function disableTwoFactor($userId, $currentPassword = null) {
        $db = Database::getInstance();
        
        // If password is provided, verify it first
        if ($currentPassword) {
            $user = $db->fetchRow("SELECT password_hash FROM admin_users WHERE id = :id", [':id' => $userId]);
            if (!$user || !password_verify($currentPassword, $user['password_hash'])) {
                throw new Exception('Invalid password.');
            }
        }
        
        $result = $db->update('admin_users',
            ['2fa_secret' => null, '2fa_enabled' => 0],
            'id = :id',
            [':id' => $userId]
        );
        
        if ($result === 0) {
            throw new Exception('Failed to disable two-factor authentication.');
        }
        
        // Log the 2FA disablement
        $logger = new SecurityLogger();
        $logger->logTwoFactorDisabled($userId, $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1');
        
        return true;
    }
    
    /**
     * Generate backup codes for 2FA
     */
    public function generateBackupCodes($count = 10) {
        $codes = [];
        
        for ($i = 0; $i < $count; $i++) {
            // Generate 8-character alphanumeric codes
            $code = '';
            $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            
            for ($j = 0; $j < 8; $j++) {
                $code .= $chars[random_int(0, strlen($chars) - 1)];
            }
            
            $codes[] = $code;
        }
        
        return $codes;
    }
    
    /**
     * Store backup codes for a user
     */
    public function storeBackupCodes($userId, $codes) {
        $db = Database::getInstance();
        
        // Hash the backup codes before storing
        $hashedCodes = array_map(function($code) {
            return password_hash($code, PASSWORD_DEFAULT);
        }, $codes);
        
        $result = $db->update('admin_users',
            ['backup_codes' => json_encode($hashedCodes)],
            'id = :id',
            [':id' => $userId]
        );
        
        return $result > 0;
    }
    
    /**
     * Verify backup code
     */
    public function verifyBackupCode($userId, $code) {
        $db = Database::getInstance();
        
        $user = $db->fetchRow("SELECT backup_codes FROM admin_users WHERE id = :id", [':id' => $userId]);
        if (!$user || !$user['backup_codes']) {
            return false;
        }
        
        $backupCodes = json_decode($user['backup_codes'], true);
        if (!$backupCodes) {
            return false;
        }
        
        foreach ($backupCodes as $index => $hashedCode) {
            if (password_verify($code, $hashedCode)) {
                // Remove used backup code
                unset($backupCodes[$index]);
                
                $db->update('admin_users',
                    ['backup_codes' => json_encode(array_values($backupCodes))],
                    'id = :id',
                    [':id' => $userId]
                );
                
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get remaining backup codes count
     */
    public function getRemainingBackupCodesCount($userId) {
        $db = Database::getInstance();
        
        $user = $db->fetchRow("SELECT backup_codes FROM admin_users WHERE id = :id", [':id' => $userId]);
        if (!$user || !$user['backup_codes']) {
            return 0;
        }
        
        $backupCodes = json_decode($user['backup_codes'], true);
        return $backupCodes ? count($backupCodes) : 0;
    }
    
    /**
     * Check if user has 2FA enabled
     */
    public function isTwoFactorEnabled($userId) {
        $db = Database::getInstance();
        
        $user = $db->fetchRow("SELECT 2fa_enabled FROM admin_users WHERE id = :id", [':id' => $userId]);
        return $user && $user['2fa_enabled'];
    }
    
    /**
     * Get time remaining until next code
     */
    public function getTimeRemaining() {
        return 30 - (time() % 30);
    }
}
?>
