<?php
/**
 * Modern Admin Login Page
 *
 * Beautiful, responsive login interface with enhanced UX
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Load configuration and classes
require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/classes/Database.php';

$error = '';
$success = '';

// Handle logout
if (isset($_POST['logout']) || isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit;
}

// Check if already logged in
$isLoggedIn = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'];

// Handle login
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    try {
        $username = trim($_POST['username']);
        $password = $_POST['password'];

        if (empty($username) || empty($password)) {
            throw new Exception('Username and password are required.');
        }

        // Get database instance
        $db = Database::getInstance();

        // Find user
        $stmt = $db->query("SELECT * FROM admin_users WHERE (username = ? OR email = ?) AND is_active = 1", [$username, $username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$user) {
            throw new Exception('Invalid username or password.');
        }

        // Verify password
        if (!password_verify($password, $user['password_hash'])) {
            throw new Exception('Invalid username or password.');
        }

        // Update last login
        $db->query("UPDATE admin_users SET last_login = NOW() WHERE id = ?", [$user['id']]);

        // Set session
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_email'] = $user['email'];
        $_SESSION['admin_role'] = $user['role'];

        $success = 'Login successful! Welcome back, ' . htmlspecialchars($user['username']);
        $isLoggedIn = true;

    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Login - AI Graph Search</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .input-focus:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.25);
        }
    </style>
</head>
<body class="min-h-screen gradient-bg flex items-center justify-center p-4">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, white 2px, transparent 2px), radial-gradient(circle at 75% 75%, white 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <!-- Main Container -->
    <div class="relative z-10 w-full max-w-md animate-slide-up">
        <!-- Logo Section -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center justify-center w-20 h-20 bg-white rounded-full shadow-lg mb-4 animate-pulse-slow">
                <i class="fas fa-shield-alt text-3xl text-primary-600"></i>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">AI Graph Search</h1>
            <p class="text-blue-100">Secure Authentication Portal</p>
        </div>

        <!-- Login Card -->
        <div class="glass-effect rounded-2xl shadow-2xl p-8 animate-fade-in">
            <?php if ($error): ?>
                <div class="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded-lg mb-6 animate-slide-up">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle mr-3"></i>
                        <span><?php echo htmlspecialchars($error); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-50 border-l-4 border-green-500 text-green-700 p-4 rounded-lg mb-6 animate-slide-up">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo htmlspecialchars($success); ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($isLoggedIn): ?>
                <!-- Logged In State -->
                <div class="text-center animate-fade-in">
                    <div class="w-24 h-24 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="fas fa-user text-white text-2xl"></i>
                    </div>
                    
                    <h2 class="text-2xl font-bold text-gray-800 mb-8">
                        Welcome back!
                    </h2>

                    <div class="space-y-4">
                        <a href="dashboard.php" class="block w-full bg-primary-600 text-white py-4 px-6 rounded-xl hover:bg-primary-700 transition-all duration-300 btn-hover font-semibold">
                            <i class="fas fa-tachometer-alt mr-3"></i>Access Dashboard
                        </a>

                        <a href="<?php echo getConfig('app', 'root_url'); ?>/graphsearch.php" class="block w-full bg-purple-600 text-white py-4 px-6 rounded-xl hover:bg-purple-700 transition-all duration-300 btn-hover font-semibold">
                            <i class="fas fa-search mr-3"></i>Launch Search
                        </a>

                        <div class="pt-4 border-t border-gray-200">
                            <form method="POST" class="w-full">
                                <button type="submit" name="logout" class="w-full bg-gray-600 text-white py-3 px-6 rounded-xl hover:bg-gray-700 transition-all duration-300 btn-hover">
                                    <i class="fas fa-sign-out-alt mr-3"></i>Logout
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

            <?php else: ?>
                <!-- Login Form -->
                <form method="POST" class="space-y-6">
                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-3">
                            <i class="fas fa-user mr-2 text-primary-600"></i>Username or Email
                        </label>
                        <input type="text" name="username" required
                               class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:outline-none transition-all duration-300 input-focus"
                               placeholder="Enter your username or email"
                               value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>">
                    </div>

                    <div>
                        <label class="block text-gray-700 text-sm font-semibold mb-3">
                            <i class="fas fa-lock mr-2 text-primary-600"></i>Password
                        </label>
                        <div class="relative">
                            <input type="password" name="password" required id="password"
                                   class="w-full px-4 py-4 border-2 border-gray-200 rounded-xl focus:border-primary-500 focus:outline-none transition-all duration-300 input-focus pr-12"
                                   placeholder="Enter your password">
                            <button type="button" onclick="togglePassword()" class="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600">
                                <i class="fas fa-eye" id="toggleIcon"></i>
                            </button>
                        </div>
                    </div>

                    <button type="submit" name="login" class="w-full bg-primary-600 text-white py-4 px-6 rounded-xl hover:bg-primary-700 transition-all duration-300 btn-hover font-semibold text-lg">
                        <i class="fas fa-sign-in-alt mr-3"></i>Sign In
                    </button>
                </form>

                <!-- Password Reset Link -->
                <div class="mt-6 text-center">
                    <a href="forgot-password.php" class="text-primary-600 hover:text-primary-700 text-sm font-medium transition-colors">
                        <i class="fas fa-key mr-2"></i>Forgot your password?
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Footer -->
        <div class="mt-8 text-center">
            <div class="text-blue-200 text-sm">
                © 2025 AI Graph Search System v2.0
            </div>
        </div>
    </div>

    <script>
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }



        // Add loading animation on form submit
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('button[name="login"]');
            if (submitBtn) {
                // Don't disable the button immediately, let the form submit first
                setTimeout(() => {
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-3"></i>Signing In...';
                    submitBtn.disabled = true;
                }, 100);
            }
        });

        // Add entrance animations
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.animate-slide-up');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });
        });
    </script>
</body>
</html>
