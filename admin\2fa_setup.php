<?php
/**
 * 2FA Setup Handler
 * 
 * Handles AJAX requests for two-factor authentication setup
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => 'Not authenticated']);
    exit;
}

// Database configuration
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'graphDB';

header('Content-Type: application/json');

try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $action = $_GET['action'] ?? '';
    $userId = $_SESSION['admin_user_id'];
    $username = $_SESSION['admin_username'];
    
    switch ($action) {
        case 'generate':
            // Generate new 2FA secret
            $secret = generateSecret();
            
            // Store secret temporarily in session
            $_SESSION['temp_2fa_secret'] = $secret;
            
            // Generate QR code URL
            $issuer = 'GraphDB Admin';
            $label = urlencode($issuer . ':' . $username);
            $issuer = urlencode($issuer);
            $otpauth = "otpauth://totp/{$label}?secret={$secret}&issuer={$issuer}";
            $qrUrl = "https://chart.googleapis.com/chart?chs=200x200&chld=M|0&cht=qr&chl=" . urlencode($otpauth);
            
            echo json_encode([
                'success' => true,
                'secret' => $secret,
                'qr_url' => $qrUrl
            ]);
            break;
            
        case 'enable':
            $input = json_decode(file_get_contents('php://input'), true);
            $secret = $input['secret'] ?? '';
            $code = $input['code'] ?? '';
            
            if (empty($secret) || empty($code)) {
                throw new Exception('Secret and verification code are required');
            }
            
            // Verify the code
            if (!verifyTOTP($secret, $code)) {
                throw new Exception('Invalid verification code. Please try again.');
            }
            
            // Enable 2FA for user
            $stmt = $pdo->prepare("UPDATE admin_users SET 2fa_secret = ?, 2fa_enabled = 1 WHERE id = ?");
            $result = $stmt->execute([$secret, $userId]);
            
            if (!$result) {
                throw new Exception('Failed to enable two-factor authentication');
            }
            
            // Clear temporary secret
            unset($_SESSION['temp_2fa_secret']);
            
            echo json_encode([
                'success' => true,
                'message' => '2FA enabled successfully'
            ]);
            break;
            
        case 'disable':
            // Disable 2FA for user
            $stmt = $pdo->prepare("UPDATE admin_users SET 2fa_secret = NULL, 2fa_enabled = 0 WHERE id = ?");
            $result = $stmt->execute([$userId]);
            
            if (!$result) {
                throw new Exception('Failed to disable two-factor authentication');
            }
            
            echo json_encode([
                'success' => true,
                'message' => '2FA disabled successfully'
            ]);
            break;
            
        case 'status':
            // Get current 2FA status
            $stmt = $pdo->prepare("SELECT 2fa_enabled FROM admin_users WHERE id = ?");
            $stmt->execute([$userId]);
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'enabled' => (bool)$user['2fa_enabled']
            ]);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Generate a random secret for TOTP
 */
function generateSecret($length = 32) {
    $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $secret = '';
    
    for ($i = 0; $i < $length; $i++) {
        $secret .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    return $secret;
}

/**
 * Verify TOTP code
 */
function verifyTOTP($secret, $code, $window = 2) {
    $timestamp = time();
    
    // Remove any spaces or formatting from the code
    $code = preg_replace('/\s+/', '', $code);
    
    if (strlen($code) !== 6 || !ctype_digit($code)) {
        return false;
    }
    
    // Check current time window and adjacent windows
    for ($i = -$window; $i <= $window; $i++) {
        $timeSlice = intval($timestamp / 30) + $i;
        $calculatedCode = calculateTOTP($secret, $timeSlice);
        
        if (hash_equals($code, $calculatedCode)) {
            return true;
        }
    }
    
    return false;
}

/**
 * Calculate TOTP code for given secret and time slice
 */
function calculateTOTP($secret, $timeSlice) {
    // Convert secret from base32
    $key = base32Decode($secret);
    
    // Pack time slice as 64-bit big-endian
    $time = pack('N*', 0) . pack('N*', $timeSlice);
    
    // Generate HMAC-SHA1
    $hash = hash_hmac('sha1', $time, $key, true);
    
    // Extract dynamic binary code
    $offset = ord($hash[19]) & 0xf;
    $code = (
        ((ord($hash[$offset + 0]) & 0x7f) << 24) |
        ((ord($hash[$offset + 1]) & 0xff) << 16) |
        ((ord($hash[$offset + 2]) & 0xff) << 8) |
        (ord($hash[$offset + 3]) & 0xff)
    ) % 1000000;
    
    return str_pad($code, 6, '0', STR_PAD_LEFT);
}

/**
 * Decode base32 string
 */
function base32Decode($input) {
    $alphabet = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567';
    $output = '';
    $v = 0;
    $vbits = 0;
    
    for ($i = 0; $i < strlen($input); $i++) {
        $value = strpos($alphabet, $input[$i]);
        if ($value === false) continue;
        
        $v <<= 5;
        $v += $value;
        $vbits += 5;
        
        if ($vbits >= 8) {
            $output .= chr(($v >> ($vbits - 8)) & 255);
            $vbits -= 8;
        }
    }
    
    return $output;
}
?>
