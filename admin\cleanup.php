<?php
/**
 * System Cleanup Script
 * 
 * Removes test files and setup scripts after successful deployment.
 * Only accessible to superadmin users.
 */

require_once __DIR__ . '/bootstrap.php';

// Require superadmin authentication
if (!$auth->isAuthenticated() || !$auth->hasRole('superadmin')) {
    redirectWithMessage('login.php', 'Superadmin access required for system cleanup.', 'error');
}

$currentUser = $auth->getCurrentUser();
$logger = new SecurityLogger();

$message = '';
$messageType = '';

// Handle cleanup request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['cleanup'])) {
    if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $message = 'Invalid CSRF token.';
        $messageType = 'error';
    } else {
        $filesToCleanup = [
            'setup.php' => 'Setup script',
            'test-connection.php' => 'Connection test script',
            'test-auth.php' => 'Authentication test script',
            'security-verification.php' => 'Security verification script',
            'cleanup.php' => 'This cleanup script'
        ];

        $cleanedFiles = [];
        $errors = [];

        foreach ($filesToCleanup as $file => $description) {
            $filePath = __DIR__ . '/' . $file;
            if (file_exists($filePath)) {
                if (unlink($filePath)) {
                    $cleanedFiles[] = $description;
                } else {
                    $errors[] = "Failed to delete: $description";
                }
            }
        }

        // Log cleanup action
        $logger->logAdminAction(
            $currentUser['id'], 
            'system_cleanup', 
            'Cleaned up test files: ' . implode(', ', $cleanedFiles), 
            getClientIP()
        );

        if (!empty($cleanedFiles)) {
            $message = 'Successfully cleaned up: ' . implode(', ', $cleanedFiles);
            $messageType = 'success';
        }

        if (!empty($errors)) {
            $message .= (!empty($message) ? ' | ' : '') . 'Errors: ' . implode(', ', $errors);
            $messageType = 'warning';
        }
    }
}

// Check which files exist
$testFiles = [
    'setup.php' => [
        'name' => 'Setup Script',
        'description' => 'Initial system setup and admin account creation',
        'exists' => file_exists(__DIR__ . '/setup.php'),
        'risk' => 'HIGH'
    ],
    'test-connection.php' => [
        'name' => 'Connection Test',
        'description' => 'Database and system requirements testing',
        'exists' => file_exists(__DIR__ . '/test-connection.php'),
        'risk' => 'MEDIUM'
    ],
    'test-auth.php' => [
        'name' => 'Authentication Test',
        'description' => 'Authentication system testing',
        'exists' => file_exists(__DIR__ . '/test-auth.php'),
        'risk' => 'HIGH'
    ],
    'security-verification.php' => [
        'name' => 'Security Verification',
        'description' => 'Comprehensive security testing',
        'exists' => file_exists(__DIR__ . '/security-verification.php'),
        'risk' => 'HIGH'
    ]
];

$existingFiles = array_filter($testFiles, function($file) { return $file['exists']; });
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Cleanup - <?php echo getConfig('app', 'app_name'); ?></title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-3xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">System Cleanup</h1>
                    <p class="text-gray-600">Remove test files and setup scripts for production security</p>
                    <div class="mt-4">
                        <span class="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full">
                            <i class="fas fa-shield-alt mr-2"></i>Superadmin Only
                        </span>
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <?php if ($message): ?>
                <div class="mb-8 bg-<?php echo $messageType === 'error' ? 'red' : ($messageType === 'warning' ? 'yellow' : 'green'); ?>-100 border border-<?php echo $messageType === 'error' ? 'red' : ($messageType === 'warning' ? 'yellow' : 'green'); ?>-400 text-<?php echo $messageType === 'error' ? 'red' : ($messageType === 'warning' ? 'yellow' : 'green'); ?>-700 px-4 py-3 rounded">
                    <?php echo htmlspecialchars($message); ?>
                </div>
            <?php endif; ?>

            <!-- File Status -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Test Files Status</h2>
                
                <?php if (empty($existingFiles)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                        <h3 class="text-xl font-semibold text-green-800 mb-2">System Clean</h3>
                        <p class="text-green-600">All test files have been removed. Your system is production-ready!</p>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php foreach ($testFiles as $filename => $file): ?>
                            <?php if ($file['exists']): ?>
                                <div class="border rounded-lg p-4 <?php echo $file['risk'] === 'HIGH' ? 'border-red-200 bg-red-50' : 'border-yellow-200 bg-yellow-50'; ?>">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-code <?php echo $file['risk'] === 'HIGH' ? 'text-red-600' : 'text-yellow-600'; ?> mr-3"></i>
                                            <div>
                                                <h3 class="font-semibold <?php echo $file['risk'] === 'HIGH' ? 'text-red-800' : 'text-yellow-800'; ?>">
                                                    <?php echo htmlspecialchars($file['name']); ?>
                                                </h3>
                                                <p class="text-sm <?php echo $file['risk'] === 'HIGH' ? 'text-red-600' : 'text-yellow-600'; ?>">
                                                    <?php echo htmlspecialchars($file['description']); ?>
                                                </p>
                                                <p class="text-xs <?php echo $file['risk'] === 'HIGH' ? 'text-red-500' : 'text-yellow-500'; ?>">
                                                    File: <?php echo htmlspecialchars($filename); ?>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="text-right">
                                            <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $file['risk'] === 'HIGH' ? 'bg-red-100 text-red-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                                <?php echo $file['risk']; ?> RISK
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <?php if (!empty($existingFiles)): ?>
                <!-- Security Warning -->
                <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-8">
                    <h3 class="text-red-800 font-bold text-lg mb-3">
                        <i class="fas fa-exclamation-triangle mr-2"></i>Security Warning
                    </h3>
                    <div class="text-red-700 space-y-2">
                        <p><strong>Test files detected!</strong> These files expose sensitive system information and should be removed before production deployment.</p>
                        <ul class="list-disc list-inside space-y-1 ml-4">
                            <li><strong>Setup scripts</strong> can be used to recreate admin accounts</li>
                            <li><strong>Test files</strong> reveal system configuration and security details</li>
                            <li><strong>Debug scripts</strong> may expose authentication tokens and session data</li>
                        </ul>
                        <p><strong>Recommendation:</strong> Remove all test files immediately after verifying system functionality.</p>
                    </div>
                </div>

                <!-- Cleanup Action -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-6">Cleanup Action</h2>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                        <h3 class="text-yellow-800 font-semibold mb-2">
                            <i class="fas fa-info-circle mr-2"></i>Before Cleanup
                        </h3>
                        <ul class="text-yellow-700 text-sm space-y-1">
                            <li>✓ Verify the admin authentication system is working correctly</li>
                            <li>✓ Test GraphSearch access and functionality</li>
                            <li>✓ Confirm all security features are operational</li>
                            <li>✓ Backup your database and configuration files</li>
                        </ul>
                    </div>

                    <form method="POST" onsubmit="return confirm('Are you sure you want to delete all test files? This action cannot be undone.');">
                        <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                        <div class="text-center">
                            <button type="submit" name="cleanup" class="bg-red-600 text-white px-8 py-3 rounded-lg hover:bg-red-700 font-semibold">
                                <i class="fas fa-trash-alt mr-2"></i>Clean Up Test Files
                            </button>
                            <p class="text-sm text-gray-600 mt-2">
                                This will permanently delete all test and setup files
                            </p>
                        </div>
                    </form>
                </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="mt-8 text-center">
                <a href="dashboard.php" class="inline-block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700">
                    <i class="fas fa-tachometer-alt mr-2"></i>Return to Dashboard
                </a>
            </div>
        </div>
    </div>
</body>
</html>
