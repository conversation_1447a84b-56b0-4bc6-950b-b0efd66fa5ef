<?php
/**
 * License Migration Script
 * 
 * Migrates licenses from localStorage to database storage
 * Run this once after implementing the new license management system
 */

session_start();
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Check if user is authenticated admin
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    die('Access denied. Please log in as admin.');
}

// Database configuration
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'graphDB';

try {
    // Connect to database
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Include the LicenseManager class
    require_once __DIR__ . '/classes/Database.php';
    require_once __DIR__ . '/classes/LicenseManager.php';
    
    $licenseManager = new LicenseManager($pdo);
    
    echo "<h1>License Migration Tool</h1>";
    echo "<p>This tool will help migrate licenses from localStorage to the database.</p>";
    
    // Check if licenses table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'licenses'");
    if ($stmt->rowCount() == 0) {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>Error:</strong> Licenses table does not exist. Please run the database setup script first.";
        echo "</div>";
        echo "<p><a href='database/setup.sql'>View Database Setup Script</a></p>";
        exit;
    }
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "<strong>Success:</strong> Licenses table found in database.";
    echo "</div>";
    
    // Check current license count in database
    $stmt = $pdo->query("SELECT COUNT(*) FROM licenses");
    $dbLicenseCount = $stmt->fetchColumn();
    
    echo "<h2>Current Status</h2>";
    echo "<p><strong>Licenses in database:</strong> $dbLicenseCount</p>";
    
    // Show migration form
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['migrate'])) {
        echo "<h2>Migration Process</h2>";
        
        // Get localStorage data from the form
        $localStorageData = $_POST['localStorage_data'] ?? '';
        
        if (empty($localStorageData)) {
            echo "<div style='color: orange; padding: 10px; border: 1px solid orange; margin: 10px 0;'>";
            echo "<strong>Warning:</strong> No localStorage data provided. Nothing to migrate.";
            echo "</div>";
        } else {
            try {
                $licenses = json_decode($localStorageData, true);
                
                if (!is_array($licenses)) {
                    throw new Exception('Invalid license data format');
                }
                
                $migratedCount = 0;
                $skippedCount = 0;
                $errorCount = 0;
                
                foreach ($licenses as $license) {
                    try {
                        // Check if license already exists
                        $stmt = $pdo->prepare("SELECT id FROM licenses WHERE license_key = ?");
                        $stmt->execute([$license['key'] ?? '']);
                        
                        if ($stmt->rowCount() > 0) {
                            echo "<p style='color: orange;'>Skipped: License {$license['key']} already exists</p>";
                            $skippedCount++;
                            continue;
                        }
                        
                        // Convert localStorage format to database format
                        $licenseData = [
                            'customer_name' => $license['customerName'] ?? 'Unknown',
                            'customer_email' => $license['customerEmail'] ?? '<EMAIL>',
                            'license_type' => $license['type'] ?? 'basic',
                            'max_devices' => $license['maxDevices'] ?? 3,
                            'duration_months' => $license['duration'] ?? 12,
                            'notes' => $license['notes'] ?? 'Migrated from localStorage',
                            'created_by' => $_SESSION['admin_user_id'] ?? null
                        ];
                        
                        // Handle dates
                        if (isset($license['expiryDate'])) {
                            $expiryDate = new DateTime($license['expiryDate']);
                        } else {
                            $expiryDate = new DateTime();
                            $expiryDate->add(new DateInterval('P' . $licenseData['duration_months'] . 'M'));
                        }
                        
                        // Insert directly into database (bypass key generation since we have existing key)
                        $sql = "INSERT INTO licenses (license_key, customer_name, customer_email, license_type, 
                                status, max_devices, duration_months, expires_at, notes, created_by, total_searches, 
                                last_used, license_data) 
                                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
                        
                        $stmt = $pdo->prepare($sql);
                        $stmt->execute([
                            $license['key'] ?? 'MIGRATED-' . uniqid(),
                            $licenseData['customer_name'],
                            $licenseData['customer_email'],
                            $licenseData['license_type'],
                            $license['status'] ?? 'active',
                            $licenseData['max_devices'],
                            $licenseData['duration_months'],
                            $expiryDate->format('Y-m-d H:i:s'),
                            $licenseData['notes'],
                            $licenseData['created_by'],
                            $license['usage']['totalSearches'] ?? 0,
                            isset($license['usage']['lastUsed']) ? date('Y-m-d H:i:s', strtotime($license['usage']['lastUsed'])) : null,
                            json_encode([
                                'migrated_from' => 'localStorage',
                                'original_data' => $license,
                                'migration_date' => date('Y-m-d H:i:s')
                            ])
                        ]);
                        
                        echo "<p style='color: green;'>Migrated: License for {$licenseData['customer_name']} ({$licenseData['customer_email']})</p>";
                        $migratedCount++;
                        
                    } catch (Exception $e) {
                        echo "<p style='color: red;'>Error migrating license: " . $e->getMessage() . "</p>";
                        $errorCount++;
                    }
                }
                
                echo "<h3>Migration Summary</h3>";
                echo "<ul>";
                echo "<li><strong>Migrated:</strong> $migratedCount licenses</li>";
                echo "<li><strong>Skipped:</strong> $skippedCount licenses (already exist)</li>";
                echo "<li><strong>Errors:</strong> $errorCount licenses</li>";
                echo "</ul>";
                
                if ($migratedCount > 0) {
                    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
                    echo "<strong>Success:</strong> Migration completed! You can now safely remove localStorage data.";
                    echo "</div>";
                }
                
            } catch (Exception $e) {
                echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
                echo "<strong>Error:</strong> " . $e->getMessage();
                echo "</div>";
            }
        }
    } else {
        // Show migration form
        ?>
        <h2>Migrate Licenses from localStorage</h2>
        <p>To migrate your existing licenses, please:</p>
        <ol>
            <li>Open your browser's Developer Tools (F12)</li>
            <li>Go to the Console tab</li>
            <li>Type: <code>localStorage.getItem('adminLicenses')</code></li>
            <li>Copy the result and paste it in the textarea below</li>
        </ol>
        
        <form method="POST" style="margin: 20px 0;">
            <div style="margin: 10px 0;">
                <label for="localStorage_data"><strong>localStorage Data:</strong></label><br>
                <textarea name="localStorage_data" id="localStorage_data" rows="10" cols="80" 
                          placeholder="Paste your localStorage data here..."></textarea>
            </div>
            <div style="margin: 10px 0;">
                <button type="submit" name="migrate" style="padding: 10px 20px; background: #007cba; color: white; border: none; cursor: pointer;">
                    Migrate Licenses
                </button>
            </div>
        </form>
        
        <h2>Alternative: JavaScript Migration</h2>
        <p>You can also run this JavaScript code in the console on any admin page to automatically migrate:</p>
        <pre style="background: #f5f5f5; padding: 10px; border: 1px solid #ddd;">
// Get licenses from localStorage
const licenses = JSON.parse(localStorage.getItem('adminLicenses') || '[]');
console.log('Found', licenses.length, 'licenses to migrate');

// Send to migration endpoint
if (licenses.length > 0) {
    fetch('migrate_licenses.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: 'migrate=1&localStorage_data=' + encodeURIComponent(JSON.stringify(licenses))
    })
    .then(response => response.text())
    .then(data => {
        console.log('Migration response:', data);
        // Optionally clear localStorage after successful migration
        // localStorage.removeItem('adminLicenses');
    });
}
        </pre>
        <?php
    }
    
    // Show current licenses in database
    echo "<h2>Current Licenses in Database</h2>";
    $licenses = $licenseManager->getLicenses(['limit' => 10]);
    
    if (empty($licenses)) {
        echo "<p>No licenses found in database.</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Customer</th><th>Email</th><th>Type</th><th>Status</th><th>Expires</th></tr>";
        
        foreach ($licenses as $license) {
            echo "<tr>";
            echo "<td>{$license['id']}</td>";
            echo "<td>" . htmlspecialchars($license['customer_name']) . "</td>";
            echo "<td>" . htmlspecialchars($license['customer_email']) . "</td>";
            echo "<td>{$license['license_type']}</td>";
            echo "<td>{$license['status']}</td>";
            echo "<td>{$license['expires_at']}</td>";
            echo "</tr>";
        }
        
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "<strong>Database Error:</strong> " . $e->getMessage();
    echo "</div>";
}
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

table {
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
}

th {
    background-color: #f2f2f2;
}

code {
    background-color: #f5f5f5;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>
