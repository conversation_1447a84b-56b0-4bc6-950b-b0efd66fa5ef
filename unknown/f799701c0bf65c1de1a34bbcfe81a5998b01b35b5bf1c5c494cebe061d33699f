<?php
session_start();

// Check if already logged in
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    header('Location: graphsearch.php');
    exit();
}

// Handle login
if ($_POST) {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';
    
    // Simple authentication (in production, use proper password hashing)
    if ($username === 'admin' && $password === 'facebook2024') {
        $_SESSION['admin_logged_in'] = true;
        $_SESSION['admin_username'] = $username;
        $_SESSION['login_time'] = time();
        header('Location: graphsearch.php');
        exit();
    } else {
        $error = 'Invalid username or password';
    }
}
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Graph Search - Admin Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        facebook: {
                            blue: '#1877F2',
                            darkBlue: '#166FE5'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .login-card {
            background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.1);
        }
        .dark .login-card {
            background: rgba(31, 41, 55, 0.95);
            border-color: rgba(75, 85, 99, 0.3);
        }
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .dark body {
            background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <div class="login-card rounded-2xl shadow-2xl p-8 w-full max-w-md">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-16 h-16 bg-gradient-to-br from-facebook-blue to-facebook-darkBlue rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg">
                <i class="fab fa-facebook-f text-white text-2xl"></i>
            </div>
            <h1 class="text-2xl font-bold text-white mb-2">Facebook Graph Search</h1>
            <p class="text-gray-300">Admin Dashboard Login</p>
        </div>

        <!-- Error Message -->
        <?php if (isset($error)): ?>
        <div class="bg-red-500/20 border border-red-500/30 text-red-200 px-4 py-3 rounded-lg mb-6">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle mr-2"></i>
                <?php echo htmlspecialchars($error); ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Login Form -->
        <form method="POST" class="space-y-6">
            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-user mr-2"></i>Username
                </label>
                <input type="text" name="username" required
                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-facebook-blue focus:border-transparent transition-all duration-200"
                       placeholder="Enter your username">
            </div>

            <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">
                    <i class="fas fa-lock mr-2"></i>Password
                </label>
                <input type="password" name="password" required
                       class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-facebook-blue focus:border-transparent transition-all duration-200"
                       placeholder="Enter your password">
            </div>

            <button type="submit"
                    class="w-full bg-gradient-to-r from-facebook-blue to-facebook-darkBlue text-white py-3 px-4 rounded-lg font-medium hover:from-facebook-darkBlue hover:to-facebook-blue transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl">
                <i class="fas fa-sign-in-alt mr-2"></i>Login to Admin Dashboard
            </button>
        </form>

        <!-- Demo Credentials -->
        <div class="mt-8 p-4 bg-blue-500/20 border border-blue-500/30 rounded-lg">
            <h3 class="text-sm font-medium text-blue-200 mb-2">Demo Credentials:</h3>
            <div class="text-xs text-blue-300 space-y-1">
                <div><strong>Username:</strong> admin</div>
                <div><strong>Password:</strong> facebook2024</div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-xs text-gray-400">
            <p>&copy; 2024 Facebook Graph Search Admin. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
