<?php
/**
 * Admin System Bootstrap
 * 
 * Initializes the admin authentication system, loads configuration,
 * and sets up security headers and session management.
 */

// Prevent direct access
define('ADMIN_SYSTEM_LOADED', true);

// Start output buffering
ob_start();

// Load configuration
require_once __DIR__ . '/config/config.php';

// Load core classes
require_once __DIR__ . '/classes/Database.php';
require_once __DIR__ . '/classes/Auth.php';
require_once __DIR__ . '/classes/TwoFactorAuth.php';
require_once __DIR__ . '/classes/SecurityLogger.php';

// Set security headers
function setSecurityHeaders() {
    $headers = getConfig('security', 'security_headers', []);
    
    foreach ($headers as $header => $value) {
        header("$header: $value");
    }
    
    // Additional security headers
    header('X-Powered-By: '); // Remove PHP version disclosure
    header('Server: '); // Remove server information
}

// Initialize CSRF protection
function initCSRF() {
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        regenerateCSRFToken();
    } else {
        // Check if token needs regeneration
        $tokenAge = time() - $_SESSION['csrf_token_time'];
        $regenerateInterval = getConfig('security', 'csrf_regenerate_interval', 300);
        
        if ($tokenAge > $regenerateInterval) {
            regenerateCSRFToken();
        }
    }
}

// Generate new CSRF token
function regenerateCSRFToken() {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    $_SESSION['csrf_token_time'] = time();
}

// Validate CSRF token
function validateCSRFToken($token) {
    if (!isset($_SESSION['csrf_token']) || !$token) {
        return false;
    }
    
    // Check token expiry
    $tokenAge = time() - ($_SESSION['csrf_token_time'] ?? 0);
    $tokenExpiry = getConfig('security', 'csrf_token_expiry', 3600);
    
    if ($tokenAge > $tokenExpiry) {
        regenerateCSRFToken();
        return false;
    }
    
    return hash_equals($_SESSION['csrf_token'], $token);
}

// Get CSRF token for forms
function getCSRFToken() {
    return $_SESSION['csrf_token'] ?? '';
}

// Sanitize input data
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

// Validate email format
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

// Generate secure random string
function generateSecureToken($length = 32) {
    return bin2hex(random_bytes($length));
}

// Check if request is AJAX
function isAjaxRequest() {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

// Send JSON response
function sendJsonResponse($data, $statusCode = 200) {
    http_response_code($statusCode);
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Redirect with message
function redirectWithMessage($url, $message, $type = 'info') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit;
}

// Get and clear flash message
function getFlashMessage() {
    $message = $_SESSION['flash_message'] ?? null;
    $type = $_SESSION['flash_type'] ?? 'info';
    
    unset($_SESSION['flash_message']);
    unset($_SESSION['flash_type']);
    
    return $message ? ['message' => $message, 'type' => $type] : null;
}

// Format date for display
function formatDate($date, $format = null) {
    if (!$date) return '';
    
    $format = $format ?: getConfig('app', 'date_format', 'Y-m-d H:i:s');
    
    if (is_string($date)) {
        $date = new DateTime($date);
    }
    
    return $date->format($format);
}

// Get client IP address
function getClientIP() {
    $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
    
    foreach ($ipKeys as $key) {
        if (!empty($_SERVER[$key])) {
            $ip = $_SERVER[$key];
            if (strpos($ip, ',') !== false) {
                $ip = trim(explode(',', $ip)[0]);
            }
            if (filter_var($ip, FILTER_VALIDATE_IP)) {
                return $ip;
            }
        }
    }
    
    return '127.0.0.1';
}

// Initialize error handling
function initErrorHandling() {
    set_error_handler(function($severity, $message, $file, $line) {
        if (!(error_reporting() & $severity)) {
            return false;
        }
        
        $errorTypes = [
            E_ERROR => 'ERROR',
            E_WARNING => 'WARNING',
            E_NOTICE => 'NOTICE',
            E_USER_ERROR => 'USER_ERROR',
            E_USER_WARNING => 'USER_WARNING',
            E_USER_NOTICE => 'USER_NOTICE'
        ];
        
        $type = $errorTypes[$severity] ?? 'UNKNOWN';
        $logMessage = "[$type] $message in $file on line $line";
        
        error_log($logMessage);
        
        if (ENVIRONMENT === 'development') {
            echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
            echo "<strong>$type:</strong> $message<br>";
            echo "<small>File: $file, Line: $line</small>";
            echo "</div>";
        }
        
        return true;
    });
    
    set_exception_handler(function($exception) {
        $message = "Uncaught exception: " . $exception->getMessage();
        $file = $exception->getFile();
        $line = $exception->getLine();
        
        error_log("$message in $file on line $line");
        
        if (ENVIRONMENT === 'development') {
            echo "<div style='background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px; border-radius: 4px;'>";
            echo "<strong>Exception:</strong> " . $exception->getMessage() . "<br>";
            echo "<small>File: $file, Line: $line</small>";
            echo "<pre>" . $exception->getTraceAsString() . "</pre>";
            echo "</div>";
        } else {
            echo "<h1>System Error</h1><p>An error occurred. Please try again later.</p>";
        }
    });
}

// Initialize the system
function initializeAdminSystem() {
    // Set security headers
    setSecurityHeaders();
    
    // Initialize error handling
    initErrorHandling();
    
    // Start session with secure settings
    if (session_status() === PHP_SESSION_NONE) {
        session_start();
    }
    
    // Initialize CSRF protection
    initCSRF();
    
    // Test database connection
    try {
        $db = Database::getInstance();
        if (!$db->testConnection()) {
            throw new Exception('Database connection failed');
        }
    } catch (Exception $e) {
        if (ENVIRONMENT === 'development') {
            die("Database Error: " . $e->getMessage());
        } else {
            die("System temporarily unavailable. Please try again later.");
        }
    }
}

// Rate limiting function
function checkRateLimit($action, $maxAttempts = 10, $timeWindow = 300) {
    $ip = getClientIP();
    $key = "rate_limit_{$action}_{$ip}";
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $data = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $data['first_attempt'] > $timeWindow) {
        $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
        return true;
    }
    
    // Check if limit exceeded
    if ($data['count'] >= $maxAttempts) {
        return false;
    }
    
    // Increment counter
    $_SESSION[$key]['count']++;
    return true;
}

// Password strength validation
function validatePasswordStrength($password) {
    $config = getConfig('security');
    $errors = [];
    
    if (strlen($password) < $config['password_min_length']) {
        $errors[] = "Password must be at least {$config['password_min_length']} characters long";
    }
    
    if ($config['password_require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
        $errors[] = "Password must contain at least one uppercase letter";
    }
    
    if ($config['password_require_lowercase'] && !preg_match('/[a-z]/', $password)) {
        $errors[] = "Password must contain at least one lowercase letter";
    }
    
    if ($config['password_require_numbers'] && !preg_match('/[0-9]/', $password)) {
        $errors[] = "Password must contain at least one number";
    }
    
    if ($config['password_require_special']) {
        $specialChars = preg_quote($config['password_special_chars'], '/');
        if (!preg_match("/[$specialChars]/", $password)) {
            $errors[] = "Password must contain at least one special character";
        }
    }
    
    return $errors;
}

// Initialize the admin system
initializeAdminSystem();

// Create global auth instance
$auth = new Auth();
?>
