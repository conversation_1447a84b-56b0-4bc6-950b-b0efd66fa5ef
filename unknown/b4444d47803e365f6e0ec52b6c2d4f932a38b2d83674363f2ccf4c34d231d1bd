<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header('Location: login.php');
    exit();
}

$admin_username = $_SESSION['admin_username'] ?? 'Admin';
$login_time = $_SESSION['login_time'] ?? time();
$session_duration = time() - $login_time;

// Set session for API access if not already set
if (!isset($_SESSION['api_access'])) {
    $_SESSION['api_access'] = true;
}
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facebook Graph Search - Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        facebook: {
                            blue: '#1877F2',
                            darkBlue: '#166FE5'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .admin-card {
            background: #374151;
            border: 1px solid #4b5563;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
            color: #ffffff;
        }

        body {
            background: #1f2937;
            min-height: 100vh;
            color: #ffffff;
        }

        /* Enhanced text visibility for dark theme */
        .text-white {
            color: #ffffff !important;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .text-gray-300 {
            color: #d1d5db !important;
        }

        .text-gray-400 {
            color: #9ca3af !important;
        }

        .text-gray-600 {
            color: #6b7280 !important;
        }

        .text-gray-900 {
            color: #ffffff !important;
        }

        .stat-card {
            background: #374151;
            border: 1px solid #4b5563;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
            color: #ffffff;
        }
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
        }
        .sidebar {
            background: #374151;
            border-right: 1px solid #4b5563;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
        }
        .nav-item {
            transition: all 0.2s ease;
            color: #d1d5db;
        }
        .nav-item:hover {
            background: rgba(24, 119, 242, 0.1);
            border-left: 3px solid #1877F2;
            color: #1877F2;
        }
        .nav-item.active {
            background: rgba(24, 119, 242, 0.1);
            border-left: 3px solid #1877F2;
            color: #1877F2;
        }
    </style>
</head>
<body class="bg-gray-800 text-white">
    <!-- Header -->
    <header class="bg-gray-800 border-b border-gray-600 px-6 py-4 shadow-sm">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
                <div class="w-10 h-10 bg-gradient-to-br from-facebook-blue to-facebook-darkBlue rounded-lg flex items-center justify-center">
                    <i class="fab fa-facebook-f text-white text-lg"></i>
                </div>
                <div>
                    <h1 class="text-xl font-bold text-gray-900">Facebook Graph Search</h1>
                    <p class="text-sm text-gray-600">Admin Dashboard</p>
                </div>
            </div>
            <div class="flex items-center space-x-4">
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">Welcome, <?php echo htmlspecialchars($admin_username); ?></p>
                    <p class="text-xs text-gray-600">Session: <?php echo gmdate('H:i:s', $session_duration); ?></p>
                </div>
                <a href="?logout=1" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                    <i class="fas fa-sign-out-alt mr-2"></i>Logout
                </a>
            </div>
        </div>
    </header>

    <div class="flex h-screen">
        <!-- Sidebar -->
        <aside class="sidebar w-64 p-6">
            <nav class="space-y-2">
                <a href="#dashboard" class="nav-item active flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    <span>Dashboard</span>
                </a>
                <a href="#users" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('users')">
                    <i class="fas fa-users"></i>
                    <span>User Management</span>
                </a>
                <a href="#licenses" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('licenses')">
                    <i class="fas fa-key"></i>
                    <span>License Management</span>
                </a>
                <a href="#analytics" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('analytics')">
                    <i class="fas fa-chart-line"></i>
                    <span>Analytics</span>
                </a>
                <a href="#settings" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('settings')">
                    <i class="fas fa-cog"></i>
                    <span>System Settings</span>
                </a>
                <a href="#logs" class="nav-item flex items-center space-x-3 px-4 py-3 rounded-lg" onclick="showSection('logs')">
                    <i class="fas fa-file-alt"></i>
                    <span>System Logs</span>
                </a>
            </nav>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6 overflow-y-auto">
            <!-- Dashboard Section -->
            <div id="dashboard-section" class="section">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Welcome back, admin! 👋</h2>
                    <p class="text-gray-600">Here's what's happening with your Graph Search system today.</p>
                </div>

                <!-- Stats Grid -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Total Licenses</p>
                                <p class="text-3xl font-bold text-gray-900">2</p>
                            </div>
                            <i class="fas fa-key text-blue-500 text-3xl"></i>
                        </div>
                    </div>

                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Active Licenses</p>
                                <p class="text-3xl font-bold text-gray-900">2</p>
                            </div>
                            <i class="fas fa-check-circle text-green-500 text-3xl"></i>
                        </div>
                    </div>

                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-600">Revenue (Monthly)</p>
                                <p class="text-3xl font-bold text-gray-900">$58</p>
                            </div>
                            <i class="fas fa-dollar-sign text-purple-500 text-3xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Main Content Grid -->
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                    <!-- Quick Actions -->
                    <div class="lg:col-span-2">
                        <div class="admin-card rounded-xl p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
                            <div class="space-y-3">
                                <button onclick="showSection('licenses')" class="w-full bg-purple-600 hover:bg-purple-700 text-white px-6 py-4 rounded-lg font-medium text-left flex items-center space-x-3 transition-colors">
                                    <i class="fas fa-key text-xl"></i>
                                    <span>Launch License Management</span>
                                </button>
                                <button onclick="launchSearch()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-4 rounded-lg font-medium text-left flex items-center space-x-3 transition-colors">
                                    <i class="fas fa-search text-xl"></i>
                                    <span>Launch Search</span>
                                </button>
                                <button onclick="showSection('settings')" class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-4 rounded-lg font-medium text-left flex items-center space-x-3 transition-colors">
                                    <i class="fas fa-cog text-xl"></i>
                                    <span>System Configuration</span>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div>
                        <div class="admin-card rounded-xl p-6 mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">System Information</h3>
                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-gray-600">PHP Version</span>
                                    <span class="text-gray-900">8.2.12</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Session Status</span>
                                    <span class="text-green-600">Active</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">User Role</span>
                                    <span class="text-gray-900">superadmin</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-gray-600">Login Time</span>
                                    <span class="text-gray-900">05:57:55</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- System Status -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-check-circle text-green-500"></i>
                                <span class="text-gray-900">Active Services</span>
                            </div>
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-shield-alt text-green-500"></i>
                                <span class="text-gray-900">Admin authentication system</span>
                            </div>
                        </div>
                    </div>

                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Features</h3>
                        <div class="space-y-4">
                            <div class="flex items-center space-x-3">
                                <i class="fas fa-plus-circle text-blue-500"></i>
                                <span class="text-gray-900">Generate new licenses</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Management Section -->
            <div id="users-section" class="section hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">User Management</h2>
                    <p class="text-gray-400">Manage user accounts and permissions</p>
                </div>

                <!-- User Actions -->
                <div class="flex space-x-4 mb-6">
                    <button onclick="addUser()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-plus mr-2"></i>Add User
                    </button>
                    <button onclick="exportUsers()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-download mr-2"></i>Export Users
                    </button>
                    <button onclick="bulkActions()" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg font-medium">
                        <i class="fas fa-tasks mr-2"></i>Bulk Actions
                    </button>
                </div>

                <!-- Users Table -->
                <div class="admin-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">User List</h3>
                        <div class="flex space-x-2">
                            <input type="text" placeholder="Search users..." class="bg-gray-700 text-white px-3 py-2 rounded-lg text-sm">
                            <select class="bg-gray-700 text-white px-3 py-2 rounded-lg text-sm">
                                <option>All Status</option>
                                <option>Active</option>
                                <option>Inactive</option>
                                <option>Suspended</option>
                            </select>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-700">
                                    <th class="text-left py-3 px-4 text-gray-400">
                                        <input type="checkbox" class="rounded">
                                    </th>
                                    <th class="text-left py-3 px-4 text-gray-400">User</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Email</th>
                                    <th class="text-left py-3 px-4 text-gray-400">License</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Status</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Last Login</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                <tr class="border-b border-gray-800 hover:bg-gray-800">
                                    <td class="py-3 px-4"><input type="checkbox" class="rounded"></td>
                                    <td class="py-3 px-4">
                                        <div class="flex items-center space-x-3">
                                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-xs font-bold">JD</div>
                                            <span class="text-white">John Doe</span>
                                        </div>
                                    </td>
                                    <td class="py-3 px-4 text-gray-300"><EMAIL></td>
                                    <td class="py-3 px-4">
                                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">PRO</span>
                                    </td>
                                    <td class="py-3 px-4">
                                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">Active</span>
                                    </td>
                                    <td class="py-3 px-4 text-gray-300">2 hours ago</td>
                                    <td class="py-3 px-4">
                                        <div class="flex space-x-2">
                                            <button class="text-blue-400 hover:text-blue-300" onclick="editUser(1)">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="text-red-400 hover:text-red-300" onclick="deleteUser(1)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- More user rows would be generated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- License Management Section -->
            <div id="licenses-section" class="section hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-gray-800 mb-2 flex items-center">
                        <i class="fas fa-key text-blue-600 mr-3"></i>
                        License Management System
                        <i class="fas fa-edit text-gray-400 ml-2 text-lg"></i>
                    </h2>
                    <p class="text-gray-600">Generate, manage, and monitor your Graph Search licenses.</p>
                </div>

                <!-- License Stats Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-blue-100 p-3 rounded-lg mr-4">
                                <i class="fas fa-key text-blue-600 text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Total Licenses</p>
                                <p class="text-2xl font-bold text-gray-800" id="totalLicensesCount">2</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-green-100 p-3 rounded-lg mr-4">
                                <i class="fas fa-check-circle text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Active Licenses</p>
                                <p class="text-2xl font-bold text-gray-800" id="activeLicensesCount">2</p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
                        <div class="flex items-center">
                            <div class="bg-yellow-100 p-3 rounded-lg mr-4">
                                <i class="fas fa-dollar-sign text-yellow-600 text-xl"></i>
                            </div>
                            <div>
                                <p class="text-sm text-gray-600">Revenue (Monthly)</p>
                                <p class="text-2xl font-bold text-gray-800" id="monthlyRevenue">$58</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Generate New License Section -->
                <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 mb-8">
                    <div class="flex items-center mb-6">
                        <div class="bg-green-100 p-2 rounded-lg mr-3">
                            <i class="fas fa-check text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">Generate New License</h3>
                    </div>

                    <form id="licenseForm" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">License Type</label>
                            <select name="license_type" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="basic">Basic ($29/mo)</option>
                                <option value="professional">Professional ($79/mo)</option>
                                <option value="enterprise">Enterprise ($199/mo)</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Customer Email</label>
                            <input type="email" name="customer_email" placeholder="<EMAIL>" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Customer Name</label>
                            <input type="text" name="customer_name" placeholder="John Doe" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Duration</label>
                            <select name="duration_months" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="1">1 Month</option>
                                <option value="3">3 Months</option>
                                <option value="6">6 Months</option>
                                <option value="12" selected>12 Months</option>
                                <option value="24">24 Months</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Max Devices</label>
                            <select name="max_devices" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                <option value="1">1 Device</option>
                                <option value="3" selected>3 Devices</option>
                                <option value="5">5 Devices</option>
                                <option value="10">10 Devices</option>
                                <option value="-1">Unlimited</option>
                            </select>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Notes (Optional)</label>
                            <textarea name="notes" placeholder="Internal notes..." rows="1" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"></textarea>
                        </div>
                    </form>

                    <div class="mt-6 flex items-center">
                        <label class="flex items-center">
                            <input type="checkbox" name="license_upgrade" class="mr-2 text-green-600 focus:ring-green-500">
                            <span class="text-sm text-gray-700">
                                <i class="fas fa-arrow-up text-green-600 mr-1"></i>
                                License Upgrade (Allow generating new license for existing email)
                            </span>
                        </label>
                    </div>

                    <div class="mt-6">
                        <button type="submit" id="generateBtn" class="w-full bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-md font-medium transition-colors" style="color: white !important;">
                            <i class="fas fa-key mr-2"></i>Generate License Key
                        </button>
                    </div>
                </div>

                <!-- License Management Table -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-list text-blue-600 mr-2"></i>
                                <h3 class="text-lg font-semibold text-gray-800">License Management</h3>
                            </div>
                            <div class="flex space-x-2">
                                <button type="button" id="exportBtn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-md text-sm font-medium" style="color: white !important;">
                                    <i class="fas fa-download mr-1"></i>Export
                                </button>
                                <button type="button" id="refreshBtn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium" style="color: white !important;">
                                    <i class="fas fa-sync mr-1"></i>Refresh
                                </button>
                            </div>
                        </div>

                        <div class="mt-4 flex space-x-4">
                            <div class="flex-1">
                                <input type="text" id="licenseSearch" placeholder="Search by email, name, or license key..." class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            <div>
                                <select id="statusFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Status</option>
                                    <option value="active">Active</option>
                                    <option value="expired">Expired</option>
                                    <option value="revoked">Revoked</option>
                                </select>
                            </div>
                            <div>
                                <select id="typeFilter" class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">All Types</option>
                                    <option value="basic">Basic</option>
                                    <option value="pro">Pro</option>
                                    <option value="enterprise">Enterprise</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">Customer</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">License Key</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">Type</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">Status</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">Expires</th>
                                    <th class="text-left py-3 px-6 text-sm font-medium text-gray-700">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="licenseTableBody" class="divide-y divide-gray-200">
                                <!-- License rows will be populated here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics-section" class="section hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">Analytics & Reports</h2>
                    <p class="text-gray-400">Detailed analytics and performance reports</p>
                </div>

                <!-- Analytics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Total Searches</p>
                                <p class="text-2xl font-bold text-white">45,892</p>
                                <p class="text-xs text-green-400">+18% this week</p>
                            </div>
                            <i class="fas fa-search text-blue-500 text-2xl"></i>
                        </div>
                    </div>
                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Success Rate</p>
                                <p class="text-2xl font-bold text-white">94.2%</p>
                                <p class="text-xs text-green-400">+2.1% improvement</p>
                            </div>
                            <i class="fas fa-check-circle text-green-500 text-2xl"></i>
                        </div>
                    </div>
                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Avg Response Time</p>
                                <p class="text-2xl font-bold text-white">1.2s</p>
                                <p class="text-xs text-blue-400">-0.3s faster</p>
                            </div>
                            <i class="fas fa-clock text-purple-500 text-2xl"></i>
                        </div>
                    </div>
                    <div class="stat-card rounded-xl p-6">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm text-gray-400">Error Rate</p>
                                <p class="text-2xl font-bold text-white">0.8%</p>
                                <p class="text-xs text-green-400">-0.2% improvement</p>
                            </div>
                            <i class="fas fa-exclamation-triangle text-orange-500 text-2xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Analytics Charts -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Search Trends (Last 30 Days)</h3>
                        <canvas id="trendsChart" width="400" height="200"></canvas>
                    </div>
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Search Types Distribution</h3>
                        <canvas id="typesChart" width="400" height="200"></canvas>
                    </div>
                </div>

                <!-- Export Options -->
                <div class="admin-card rounded-xl p-6">
                    <h3 class="text-lg font-semibold text-white mb-4">Export Reports</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <button onclick="exportReport('daily')" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium">
                            <i class="fas fa-calendar-day mr-2"></i>Daily Report
                        </button>
                        <button onclick="exportReport('weekly')" class="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium">
                            <i class="fas fa-calendar-week mr-2"></i>Weekly Report
                        </button>
                        <button onclick="exportReport('monthly')" class="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg font-medium">
                            <i class="fas fa-calendar-alt mr-2"></i>Monthly Report
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Section -->
            <div id="settings-section" class="section hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">System Settings</h2>
                    <p class="text-gray-400">Configure system parameters and preferences</p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- General Settings -->
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">General Settings</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">System Name</label>
                                <input type="text" value="Facebook Graph Search" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Admin Email</label>
                                <input type="email" value="<EMAIL>" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Timezone</label>
                                <select class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                                    <option>UTC</option>
                                    <option>EST</option>
                                    <option>PST</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Security Settings -->
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Security Settings</h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Two-Factor Authentication</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Session Timeout (minutes)</span>
                                <input type="number" value="30" class="w-20 bg-gray-700 text-white px-2 py-1 rounded text-sm">
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">Login Attempts Limit</span>
                                <input type="number" value="5" class="w-20 bg-gray-700 text-white px-2 py-1 rounded text-sm">
                            </div>
                        </div>
                    </div>

                    <!-- API Settings -->
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">API Settings</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Rate Limit (requests/minute)</label>
                                <input type="number" value="100" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-300">API Logging</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-600 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Settings -->
                    <div class="admin-card rounded-xl p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">Performance Settings</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Cache Duration (hours)</label>
                                <input type="number" value="24" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Max Concurrent Searches</label>
                                <input type="number" value="50" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Settings -->
                <div class="mt-6">
                    <button onclick="saveSettings()" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium">
                        <i class="fas fa-save mr-2"></i>Save Settings
                    </button>
                </div>
            </div>

            <!-- Logs Section -->
            <div id="logs-section" class="section hidden">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold text-white mb-2">System Logs</h2>
                    <p class="text-gray-400">Monitor system activity and troubleshoot issues</p>
                </div>

                <!-- Log Filters -->
                <div class="admin-card rounded-xl p-6 mb-6">
                    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Log Level</label>
                            <select class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                                <option>All Levels</option>
                                <option>Error</option>
                                <option>Warning</option>
                                <option>Info</option>
                                <option>Debug</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Date From</label>
                            <input type="date" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">Date To</label>
                            <input type="date" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg">
                        </div>
                        <div class="flex items-end">
                            <button onclick="filterLogs()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                                <i class="fas fa-filter mr-2"></i>Filter
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Logs Table -->
                <div class="admin-card rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">Recent Logs</h3>
                        <div class="flex space-x-2">
                            <button onclick="refreshLogs()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-refresh mr-1"></i>Refresh
                            </button>
                            <button onclick="exportLogs()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-download mr-1"></i>Export
                            </button>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="w-full text-sm">
                            <thead>
                                <tr class="border-b border-gray-700">
                                    <th class="text-left py-3 px-4 text-gray-400">Timestamp</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Level</th>
                                    <th class="text-left py-3 px-4 text-gray-400">Message</th>
                                    <th class="text-left py-3 px-4 text-gray-400">User</th>
                                    <th class="text-left py-3 px-4 text-gray-400">IP Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr class="border-b border-gray-800 hover:bg-gray-800">
                                    <td class="py-3 px-4 text-gray-300">2024-01-15 14:30:25</td>
                                    <td class="py-3 px-4">
                                        <span class="bg-green-500/20 text-green-400 px-2 py-1 rounded text-xs">INFO</span>
                                    </td>
                                    <td class="py-3 px-4 text-white">User login successful</td>
                                    <td class="py-3 px-4 text-gray-300"><EMAIL></td>
                                    <td class="py-3 px-4 text-gray-300">*************</td>
                                </tr>
                                <tr class="border-b border-gray-800 hover:bg-gray-800">
                                    <td class="py-3 px-4 text-gray-300">2024-01-15 14:28:15</td>
                                    <td class="py-3 px-4">
                                        <span class="bg-red-500/20 text-red-400 px-2 py-1 rounded text-xs">ERROR</span>
                                    </td>
                                    <td class="py-3 px-4 text-white">Failed to connect to Facebook API</td>
                                    <td class="py-3 px-4 text-gray-300">system</td>
                                    <td class="py-3 px-4 text-gray-300">-</td>
                                </tr>
                                <tr class="border-b border-gray-800 hover:bg-gray-800">
                                    <td class="py-3 px-4 text-gray-300">2024-01-15 14:25:10</td>
                                    <td class="py-3 px-4">
                                        <span class="bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded text-xs">WARNING</span>
                                    </td>
                                    <td class="py-3 px-4 text-white">High search volume detected</td>
                                    <td class="py-3 px-4 text-gray-300">system</td>
                                    <td class="py-3 px-4 text-gray-300">-</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Section Management (legacy function for onclick handlers)
        function showSection(sectionName) {
            showSectionByName(sectionName);
        }

        // User Management Functions
        function addUser() {
            alert('Add User functionality - ready for implementation');
        }

        function editUser(userId) {
            alert(`Edit User ${userId} functionality - ready for implementation`);
        }

        function deleteUser(userId) {
            if (confirm('Are you sure you want to delete this user?')) {
                alert(`Delete User ${userId} functionality - ready for implementation`);
            }
        }

        function exportUsers() {
            alert('Export Users functionality - ready for implementation');
        }

        function bulkActions() {
            alert('Bulk Actions functionality - ready for implementation');
        }

        // Dashboard Functions
        function launchSearch() {
            // Open the main search page in a new tab
            window.open('/linked/facebook/graphsearch.php', '_blank');
        }

        // License Management Functions - Fixed event handling
        function initializeLicenseEventHandlers() {
            console.log('Initializing license event handlers...');

            // Remove any existing event handlers first
            $('#licenseForm').off('submit');
            $('#refreshBtn').off('click');
            $('#exportBtn').off('click');

            // Find the Generate License Key button (it's outside the form)
            const generateBtn = Array.from(document.querySelectorAll('button')).find(btn =>
                btn.textContent.includes('Generate License Key')
            );

            if (generateBtn) {
                console.log('Generate License Key button found, adding click handler');

                // Remove any existing click handlers
                generateBtn.onclick = null;

                // Add click handler to the Generate License Key button
                generateBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Generate License Key button clicked!');

                    showNotification('Generating license...', 'info');

                    // Get form data from the form
                    const form = document.getElementById('licenseForm');
                    if (form) {
                        const formData = new FormData(form);
                        formData.append('action', 'generate_license');

                        // Send AJAX request
                        $.ajax({
                            url: 'license_api.php',
                            method: 'POST',
                            data: formData,
                            processData: false,
                            contentType: false,
                            dataType: 'json',
                            success: function(response) {
                                console.log('License generation response:', response);

                                if (response.success) {
                                    showNotification('License generated successfully: ' + response.license.license_key, 'success');

                                    // Clear form
                                    form.reset();

                                    // Refresh data silently after license generation
                                    if (typeof loadLicenses === 'function') loadLicenses(false);
                                    if (typeof updateLicenseStats === 'function') updateLicenseStats();
                                } else {
                                    showNotification(response.error || 'Failed to generate license', 'error');
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error('AJAX error:', error);
                                showNotification('Failed to generate license. Please try again.', 'error');
                            }
                        });
                    } else {
                        showNotification('Form not found!', 'error');
                    }
                });
            }

            // Handle refresh button
            $('#refreshBtn').on('click', function() {
                console.log('Refresh button clicked');
                if (typeof loadLicenses === 'function') loadLicenses(true); // Show notification for manual refresh
                if (typeof updateLicenseStats === 'function') updateLicenseStats();
            });

            // Handle export button
            $('#exportBtn').on('click', function() {
                console.log('Export button clicked');
                if (typeof exportLicenses === 'function') {
                    exportLicenses();
                } else {
                    window.open('license_api.php?action=export', '_blank');
                }
            });

            console.log('Event handlers initialized successfully');
        }

        // Legacy function for backward compatibility
        async function generateNewLicense() {
            $('#licenseForm').submit();
        }

        async function generateLicense() {
            generateNewLicense();
        }

        async function editLicense(licenseKey) {
            showLicenseModal('edit', licenseKey);
        }

        async function extendLicense(licenseKey) {
            showLicenseModal('extend', licenseKey);
        }

        async function revokeLicense(licenseKey) {
            if (confirm('Are you sure you want to revoke this license? This action cannot be undone.')) {
                try {
                    const formData = new FormData();
                    formData.append('action', 'revoke');
                    formData.append('license_key', licenseKey);

                    const response = await fetch('license_api.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        showNotification('License revoked successfully', 'success');
                        loadLicenses(); // Refresh the license table
                        updateLicenseStats(); // Update statistics
                    } else {
                        showNotification(result.error || 'Failed to revoke license', 'error');
                    }
                } catch (error) {
                    showNotification('Error revoking license: ' + error.message, 'error');
                }
            }
        }

        async function deleteLicense(licenseKey) {
            if (confirm('Are you sure you want to permanently delete this license? This action cannot be undone.')) {
                try {
                    const formData = new FormData();
                    formData.append('action', 'delete');
                    formData.append('license_key', licenseKey);

                    const response = await fetch('license_api.php', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        showNotification('License deleted successfully', 'success');
                        loadLicenses(); // Refresh the license table
                        updateLicenseStats(); // Update statistics
                    } else {
                        showNotification(result.error || 'Failed to delete license', 'error');
                    }
                } catch (error) {
                    showNotification('Error deleting license: ' + error.message, 'error');
                }
            }
        }

        async function exportLicenses() {
            console.log('Export Licenses button clicked');
            try {
                const response = await fetch('license_api.php?action=export');
                console.log('Export response status:', response.status);

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `licenses_${new Date().toISOString().split('T')[0]}.csv`;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showNotification('Licenses exported successfully', 'success');
                } else {
                    const errorText = await response.text();
                    console.error('Export error:', errorText);
                    showNotification('Failed to export licenses: ' + errorText, 'error');
                }
            } catch (error) {
                console.error('Export error:', error);
                showNotification('Error exporting licenses: ' + error.message, 'error');
            }
        }

        function bulkLicenseActions() {
            showBulkActionsModal();
        }

        // Modal Functions
        function showLicenseModal(type, licenseKey = null) {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.id = 'licenseModal';

            let modalContent = '';

            if (type === 'generate') {
                modalContent = `
                    <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-white">Generate New License</h3>
                            <button onclick="closeLicenseModal()" class="text-gray-400 hover:text-gray-300">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form id="generateLicenseForm" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">User Name</label>
                                <input type="text" name="user_name" required class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">User Email</label>
                                <input type="email" name="user_email" required class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">License Type</label>
                                <select name="type" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                                    <option value="TRIAL">Trial (7 days)</option>
                                    <option value="BASIC">Basic</option>
                                    <option value="PRO" selected>Pro</option>
                                    <option value="ENTERPRISE">Enterprise</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">Expiry (Months)</label>
                                <select name="expiry_months" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg border border-gray-600 focus:border-blue-500 focus:outline-none">
                                    <option value="1">1 Month</option>
                                    <option value="3">3 Months</option>
                                    <option value="6">6 Months</option>
                                    <option value="12" selected>12 Months</option>
                                    <option value="24">24 Months</option>
                                </select>
                            </div>

                            <div class="flex space-x-3 pt-4">
                                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                                    <i class="fas fa-plus mr-2"></i>Generate License
                                </button>
                                <button type="button" onclick="closeLicenseModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                `;
            } else if (type === 'extend') {
                modalContent = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Extend License</h3>
                            <button onclick="closeLicenseModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form id="extendLicenseForm" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">License Key</label>
                                <input type="text" name="license_key" value="${licenseKey}" readonly class="w-full bg-gray-100 text-gray-600 px-3 py-2 rounded-lg border border-gray-300">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Extend by (Months)</label>
                                <select name="months" class="w-full bg-white text-gray-800 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none">
                                    <option value="1">1 Month</option>
                                    <option value="3">3 Months</option>
                                    <option value="6">6 Months</option>
                                    <option value="12" selected>12 Months</option>
                                    <option value="24">24 Months</option>
                                </select>
                            </div>

                            <div class="flex space-x-3 pt-4">
                                <button type="submit" class="flex-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg font-medium">
                                    <i class="fas fa-clock mr-2"></i>Extend License
                                </button>
                                <button type="button" onclick="closeLicenseModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                `;
            } else if (type === 'edit') {
                modalContent = `
                    <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-xl font-semibold text-gray-800">Edit License</h3>
                            <button onclick="closeLicenseModal()" class="text-gray-400 hover:text-gray-600">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>

                        <form id="editLicenseForm" class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">License Key</label>
                                <input type="text" name="license_key" value="${licenseKey}" readonly class="w-full bg-gray-100 text-gray-600 px-3 py-2 rounded-lg border border-gray-300">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status" class="w-full bg-white text-gray-800 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none">
                                    <option value="Active">Active</option>
                                    <option value="Expired">Expired</option>
                                    <option value="Revoked">Revoked</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                                <textarea name="notes" rows="3" class="w-full bg-white text-gray-800 px-3 py-2 rounded-lg border border-gray-300 focus:border-blue-500 focus:outline-none" placeholder="Internal notes..."></textarea>
                            </div>

                            <div class="flex space-x-3 pt-4">
                                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium">
                                    <i class="fas fa-save mr-2"></i>Save Changes
                                </button>
                                <button type="button" onclick="closeLicenseModal()" class="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium">
                                    Cancel
                                </button>
                            </div>
                        </form>
                    </div>
                `;
            }

            modal.innerHTML = modalContent;
            document.body.appendChild(modal);

            // Add form submit handlers
            if (type === 'generate') {
                document.getElementById('generateLicenseForm').addEventListener('submit', handleGenerateLicense);
            } else if (type === 'extend') {
                document.getElementById('extendLicenseForm').addEventListener('submit', handleExtendLicense);
            } else if (type === 'edit') {
                document.getElementById('editLicenseForm').addEventListener('submit', handleEditLicense);
            }
        }

        function closeLicenseModal() {
            const modal = document.getElementById('licenseModal');
            if (modal) {
                modal.remove();
            }
        }

        // Notification System
        function showNotification(message, type = 'info') {
            console.log('Showing notification:', message, type);

            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 z-50 px-6 py-3 rounded-lg font-medium shadow-lg transform transition-all duration-300 translate-x-full`;
            notification.style.color = 'white';
            notification.style.zIndex = '9999';

            switch (type) {
                case 'success':
                    notification.style.backgroundColor = '#059669';
                    break;
                case 'error':
                    notification.style.backgroundColor = '#dc2626';
                    break;
                case 'warning':
                    notification.style.backgroundColor = '#d97706';
                    break;
                default:
                    notification.style.backgroundColor = '#2563eb';
            }

            notification.innerHTML = `
                <div class="flex items-center" style="color: white;">
                    <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'exclamation-triangle' : 'info'} mr-2"></i>
                    ${message}
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }

        // Form Handlers
        async function handleGenerateLicense(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            formData.append('action', 'generate');

            try {
                const response = await fetch('license_api.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('License generated successfully: ' + result.license.license_key, 'success');
                    closeLicenseModal();
                    loadLicenses(); // Refresh the license table
                    updateLicenseStats(); // Update statistics
                } else {
                    showNotification(result.error || 'Failed to generate license', 'error');
                }
            } catch (error) {
                showNotification('Error generating license: ' + error.message, 'error');
            }
        }

        async function handleExtendLicense(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            formData.append('action', 'extend');

            try {
                const response = await fetch('license_api.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(result.message, 'success');
                    closeLicenseModal();
                    loadLicenses(); // Refresh the license table
                    updateLicenseStats(); // Update statistics
                } else {
                    showNotification(result.error || 'Failed to extend license', 'error');
                }
            } catch (error) {
                showNotification('Error extending license: ' + error.message, 'error');
            }
        }

        async function handleEditLicense(e) {
            e.preventDefault();

            const formData = new FormData(e.target);
            formData.append('action', 'edit');

            try {
                const response = await fetch('license_api.php', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (result.success) {
                    showNotification('License updated successfully', 'success');
                    closeLicenseModal();
                    loadLicenses(); // Refresh the license table
                    updateLicenseStats(); // Update statistics
                } else {
                    showNotification(result.error || 'Failed to update license', 'error');
                }
            } catch (error) {
                showNotification('Error updating license: ' + error.message, 'error');
            }
        }

        // License Loading and Display Functions
        async function loadLicenses(showNotificationFlag = false) {
            console.log('Loading licenses...');
            if (showNotificationFlag) {
                showNotification('Refreshing licenses...', 'info');
            }
            try {
                const searchInput = document.getElementById('licenseSearch');
                const statusFilter = document.getElementById('statusFilter');
                const typeFilter = document.getElementById('typeFilter');

                const searchTerm = searchInput ? searchInput.value : '';
                const selectedStatus = statusFilter ? statusFilter.value : '';
                const selectedType = typeFilter ? typeFilter.value : '';

                let url = 'license_api.php?action=list';
                if (searchTerm) url += '&search=' + encodeURIComponent(searchTerm);
                if (selectedStatus) url += '&status=' + encodeURIComponent(selectedStatus);
                if (selectedType) url += '&type=' + encodeURIComponent(selectedType);

                console.log('Fetching from URL:', url);
                const response = await fetch(url);
                const result = await response.json();
                console.log('API Response:', result);

                if (result.success) {
                    displayLicenses(result.licenses);
                } else {
                    console.error('API Error:', result.error);
                    showNotification('Failed to load licenses: ' + (result.error || 'Unknown error'), 'error');
                }
            } catch (error) {
                console.error('Fetch Error:', error);
                showNotification('Error loading licenses: ' + error.message, 'error');
            }
        }

        function displayLicenses(licenses) {
            const tbody = document.getElementById('licenseTableBody');
            if (!tbody) return;

            tbody.innerHTML = '';

            licenses.forEach(license => {
                const row = document.createElement('tr');
                row.className = 'hover:bg-gray-50';

                const statusClass = license.status === 'Active' ? 'bg-green-100 text-green-800' :
                                   license.status === 'Expired' ? 'bg-red-100 text-red-800' :
                                   'bg-gray-100 text-gray-800';

                const typeClass = license.type === 'PRO' ? 'bg-blue-100 text-blue-800' :
                                 license.type === 'ENTERPRISE' ? 'bg-purple-100 text-purple-800' :
                                 license.type === 'TRIAL' ? 'bg-yellow-100 text-yellow-800' :
                                 'bg-gray-100 text-gray-800';

                // Calculate days left and format date
                const expiryDate = new Date(license.expiry_date);
                const today = new Date();
                const daysLeft = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
                const daysText = daysLeft > 0 ? `${daysLeft} days left` : 'Expired';

                // Format date as MM/DD/YYYY
                const formattedDate = expiryDate.toLocaleDateString('en-US', {
                    month: 'numeric',
                    day: 'numeric',
                    year: 'numeric'
                });

                // Calculate device usage (assuming we have used_devices field, default to 0 if not)
                const usedDevices = license.used_devices || 0;
                const maxDevices = license.max_devices || license.max_searches || 3;
                const deviceUsage = maxDevices === -1 ? 'Unlimited' : `${usedDevices}/${maxDevices}`;

                row.innerHTML = `
                    <td class="py-4 px-6">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${license.user_name}</div>
                            <div class="text-sm text-gray-500">${license.user_email}</div>
                        </div>
                    </td>
                    <td class="py-4 px-6">
                        <div class="flex items-center space-x-2">
                            <div class="bg-gray-800 text-white px-3 py-1 rounded font-mono text-xs">
                                ${license.license_key}
                            </div>
                            <button class="text-blue-600 hover:text-blue-800 p-1" onclick="copyLicenseKey('${license.license_key}')" title="Copy License Key">
                                <i class="fas fa-copy"></i>
                            </button>
                        </div>
                    </td>
                    <td class="py-4 px-6">
                        <span class="${typeClass} px-2 py-1 rounded-full text-xs font-medium">${license.type.toLowerCase()}</span>
                    </td>
                    <td class="py-4 px-6">
                        <span class="${statusClass} px-2 py-1 rounded-full text-xs font-medium">${license.status.toLowerCase()}</span>
                    </td>
                    <td class="py-4 px-6">
                        <div>
                            <div class="text-sm font-medium text-gray-900">${formattedDate}</div>
                            <div class="text-xs text-gray-500">${daysText}</div>
                            <div class="text-xs text-gray-600">Devices: ${deviceUsage}</div>
                        </div>
                    </td>
                    <td class="py-4 px-6">
                        <div class="flex space-x-1">
                            <button class="text-green-600 hover:text-green-800 p-1" onclick="extendLicense('${license.license_key}')" title="Extend License">
                                <i class="fas fa-clock"></i>
                            </button>
                            <button class="text-yellow-600 hover:text-yellow-800 p-1" onclick="editLicense('${license.license_key}')" title="Edit License">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-red-600 hover:text-red-800 p-1" onclick="revokeLicense('${license.license_key}')" title="Revoke License">
                                <i class="fas fa-ban"></i>
                            </button>
                            <button class="text-gray-600 hover:text-gray-800 p-1" onclick="deleteLicense('${license.license_key}')" title="Delete License">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // Copy license key to clipboard
        function copyLicenseKey(licenseKey) {
            navigator.clipboard.writeText(licenseKey).then(function() {
                showNotification('License key copied to clipboard!', 'success');
            }).catch(function(err) {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = licenseKey;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('License key copied to clipboard!', 'success');
            });
        }

        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        }

        // Update License Statistics
        async function updateLicenseStats() {
            try {
                const response = await fetch('license_api.php?action=stats');
                const result = await response.json();

                if (result.success) {
                    const stats = result.stats;

                    // Update the stats cards in the license section
                    const totalLicensesCount = document.getElementById('totalLicensesCount');
                    const activeLicensesCount = document.getElementById('activeLicensesCount');
                    const monthlyRevenue = document.getElementById('monthlyRevenue');

                    if (totalLicensesCount) totalLicensesCount.textContent = stats.total;
                    if (activeLicensesCount) activeLicensesCount.textContent = stats.active;
                    if (monthlyRevenue) monthlyRevenue.textContent = '$' + stats.monthly_revenue.toLocaleString();
                }
            } catch (error) {
                console.error('Error updating license stats:', error);
            }
        }

        // Analytics Functions
        function exportReport(type) {
            alert(`Export ${type} report functionality - ready for implementation`);
        }

        // Settings Functions
        function saveSettings() {
            alert('Settings saved successfully!');
        }

        // Logs Functions
        function filterLogs() {
            alert('Filter logs functionality - ready for implementation');
        }

        function refreshLogs() {
            alert('Refresh logs functionality - ready for implementation');
        }

        function exportLogs() {
            alert('Export logs functionality - ready for implementation');
        }

        // Handle URL fragments on page load
        function handleUrlFragment() {
            const hash = window.location.hash.substring(1); // Remove the # symbol
            if (hash && document.getElementById(hash + '-section')) {
                showSectionByName(hash);
            }
        }

        // Enhanced section management that works with URL fragments
        function showSectionByName(sectionName) {
            // Hide all sections
            document.querySelectorAll('.section').forEach(section => {
                section.classList.add('hidden');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionName + '-section');
            if (targetSection) {
                targetSection.classList.remove('hidden');
            }

            // Update navigation
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
                item.classList.add('text-gray-300');
                item.classList.remove('text-white');
            });

            // Find and activate the corresponding nav item
            const navItem = document.querySelector(`a[href="#${sectionName}"]`);
            if (navItem) {
                navItem.classList.add('active');
                navItem.classList.add('text-white');
                navItem.classList.remove('text-gray-300');
            }

            // Update URL hash
            window.location.hash = sectionName;
        }

        // Initialize Charts
        document.addEventListener('DOMContentLoaded', function() {
            // Handle URL fragment on page load
            handleUrlFragment();

            // Listen for hash changes
            window.addEventListener('hashchange', handleUrlFragment);

            // Initialize license management
            initializeLicenseManagement();

            // Initialize license event handlers
            if (typeof initializeLicenseEventHandlers === 'function') {
                initializeLicenseEventHandlers();
            }

            // Initialize navigation
            setTimeout(() => {
                // Fix navigation links to use proper event listeners
                const navLinks = document.querySelectorAll('nav a[href^="#"]');
                navLinks.forEach(link => {
                    const href = link.getAttribute('href');
                    const sectionId = href.substring(1); // Remove the #

                    // Add click event listener
                    link.addEventListener('click', function(e) {
                        e.preventDefault();
                        showSection(sectionId);
                    });
                });

                console.log('Navigation initialized with', navLinks.length, 'links');
            }, 500);

        // Initialize License Management
        function initializeLicenseManagement() {
            console.log('Initializing license management...');

            // Load initial license data silently
            loadLicenses(false);
            updateLicenseStats();

            // Add search functionality
            const searchInput = document.getElementById('licenseSearch');
            if (searchInput) {
                searchInput.addEventListener('input', debounce(loadLicenses, 300));
                console.log('Search input listener added');
            } else {
                console.log('Search input not found');
            }

            // Add filter functionality
            const statusFilter = document.getElementById('statusFilter');
            const typeFilter = document.getElementById('typeFilter');

            if (statusFilter) {
                statusFilter.addEventListener('change', loadLicenses);
                console.log('Status filter listener added');
            } else {
                console.log('Status filter not found');
            }

            if (typeFilter) {
                typeFilter.addEventListener('change', loadLicenses);
                console.log('Type filter listener added');
            } else {
                console.log('Type filter not found');
            }
        }

        // Navigation Functions
        function showSection(sectionId) {
            console.log('showSection called with:', sectionId);

            // Hide all sections
            const sections = document.querySelectorAll('.section');
            sections.forEach(section => {
                section.classList.add('hidden');
            });

            // Show the requested section
            const targetSection = document.getElementById(sectionId + '-section');
            if (targetSection) {
                targetSection.classList.remove('hidden');
                console.log('Showing section:', sectionId);

                // Update active nav item
                const navLinks = document.querySelectorAll('nav a');
                navLinks.forEach(link => {
                    link.classList.remove('active');
                    if (link.getAttribute('href') === '#' + sectionId) {
                        link.classList.add('active');
                    }
                });

                // Initialize section-specific functionality
                if (sectionId === 'licenses') {
                    // Initialize license management if the functions exist
                    if (typeof initializeLicenseManagement === 'function') {
                        initializeLicenseManagement();
                    }
                    if (typeof initializeLicenseEventHandlers === 'function') {
                        initializeLicenseEventHandlers();
                    }
                    if (typeof loadLicenses === 'function') {
                        loadLicenses();
                    }
                    if (typeof updateLicenseStats === 'function') {
                        updateLicenseStats();
                    }
                }
            } else {
                console.error('Section not found:', sectionId + '-section');
            }
        }

        // Debounce function for search
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Bulk Actions Modal
        function showBulkActionsModal() {
            const modal = document.createElement('div');
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            modal.id = 'bulkActionsModal';

            modal.innerHTML = `
                <div class="bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-white">Bulk Actions</h3>
                        <button onclick="closeBulkActionsModal()" class="text-gray-400 hover:text-gray-300">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="space-y-4">
                        <div class="text-sm text-gray-300 mb-4">
                            Select an action to perform on multiple licenses:
                        </div>

                        <button onclick="bulkExtendLicenses()" class="w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg font-medium text-left">
                            <i class="fas fa-clock mr-3"></i>Extend Multiple Licenses
                        </button>

                        <button onclick="bulkRevokeLicenses()" class="w-full bg-red-600 hover:bg-red-700 text-white px-4 py-3 rounded-lg font-medium text-left">
                            <i class="fas fa-ban mr-3"></i>Revoke Multiple Licenses
                        </button>

                        <button onclick="generateMultipleLicenses()" class="w-full bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg font-medium text-left">
                            <i class="fas fa-plus mr-3"></i>Generate Multiple Licenses
                        </button>

                        <button onclick="closeBulkActionsModal()" class="w-full bg-gray-600 hover:bg-gray-700 text-white px-4 py-3 rounded-lg font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
        }

        function closeBulkActionsModal() {
            const modal = document.getElementById('bulkActionsModal');
            if (modal) {
                modal.remove();
            }
        }

        // Bulk Action Functions (placeholder implementations)
        function bulkExtendLicenses() {
            closeBulkActionsModal();
            showNotification('Bulk extend functionality - coming soon!', 'info');
        }

        function bulkRevokeLicenses() {
            closeBulkActionsModal();
            showNotification('Bulk revoke functionality - coming soon!', 'info');
        }

        function generateMultipleLicenses() {
            closeBulkActionsModal();
            showNotification('Bulk generate functionality - coming soon!', 'info');
        }

            // Search Activity Chart
            const searchCtx = document.getElementById('searchChart').getContext('2d');
            new Chart(searchCtx, {
                type: 'line',
                data: {
                    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    datasets: [{
                        label: 'Searches',
                        data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                        borderColor: '#1877F2',
                        backgroundColor: 'rgba(24, 119, 242, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            ticks: {
                                color: '#9CA3AF'
                            },
                            grid: {
                                color: 'rgba(156, 163, 175, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#9CA3AF'
                            },
                            grid: {
                                color: 'rgba(156, 163, 175, 0.1)'
                            }
                        }
                    }
                }
            });

            // User Growth Chart
            const userCtx = document.getElementById('userChart').getContext('2d');
            new Chart(userCtx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'New Users',
                        data: [65, 59, 80, 81, 56, 55],
                        backgroundColor: 'rgba(34, 197, 94, 0.8)',
                        borderColor: '#22C55E',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            labels: {
                                color: '#ffffff'
                            }
                        }
                    },
                    scales: {
                        y: {
                            ticks: {
                                color: '#9CA3AF'
                            },
                            grid: {
                                color: 'rgba(156, 163, 175, 0.1)'
                            }
                        },
                        x: {
                            ticks: {
                                color: '#9CA3AF'
                            },
                            grid: {
                                color: 'rgba(156, 163, 175, 0.1)'
                            }
                        }
                    }
                }
            });

            // Trends Chart (Analytics Section)
            const trendsCtx = document.getElementById('trendsChart');
            if (trendsCtx) {
                new Chart(trendsCtx.getContext('2d'), {
                    type: 'line',
                    data: {
                        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
                        datasets: [{
                            label: 'Search Volume',
                            data: [1200, 1900, 3000, 2500],
                            borderColor: '#1877F2',
                            backgroundColor: 'rgba(24, 119, 242, 0.1)',
                            tension: 0.4
                        }, {
                            label: 'Success Rate',
                            data: [85, 90, 94, 92],
                            borderColor: '#22C55E',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                labels: {
                                    color: '#ffffff'
                                }
                            }
                        },
                        scales: {
                            y: {
                                ticks: {
                                    color: '#9CA3AF'
                                },
                                grid: {
                                    color: 'rgba(156, 163, 175, 0.1)'
                                }
                            },
                            x: {
                                ticks: {
                                    color: '#9CA3AF'
                                },
                                grid: {
                                    color: 'rgba(156, 163, 175, 0.1)'
                                }
                            }
                        }
                    }
                });
            }

            // Types Chart (Analytics Section)
            const typesCtx = document.getElementById('typesChart');
            if (typesCtx) {
                new Chart(typesCtx.getContext('2d'), {
                    type: 'doughnut',
                    data: {
                        labels: ['People Search', 'Bulk Search', 'Advanced Search', 'Quick Search'],
                        datasets: [{
                            data: [45, 25, 20, 10],
                            backgroundColor: [
                                '#1877F2',
                                '#22C55E',
                                '#F59E0B',
                                '#EF4444'
                            ],
                            borderWidth: 0
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    color: '#ffffff',
                                    padding: 20
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</body>
</html>
