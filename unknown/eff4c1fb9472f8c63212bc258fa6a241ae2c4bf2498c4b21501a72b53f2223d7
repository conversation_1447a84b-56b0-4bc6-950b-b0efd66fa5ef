
================================================================================
EMAIL LOG - 2025-08-05 13:16:15
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password.php?token=ad15087751b329d56d3dc02bb411b67686c710f261b475ffacf3b5180999ffd4
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password.php?token=ad15087751b329d56d3dc02bb411b67686c710f261b475ffacf3b5180999ffd4' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password.php?token=ad15087751b329d56d3dc02bb411b67686c710f261b475ffacf3b5180999ffd4</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================


================================================================================
EMAIL LOG - 2025-08-05 13:19:12
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password.php?token=4bb81da38670ecd192cb133bcca67302ec0f5e976cd6776e1a41da7413b0d9db
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password.php?token=4bb81da38670ecd192cb133bcca67302ec0f5e976cd6776e1a41da7413b0d9db' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password.php?token=4bb81da38670ecd192cb133bcca67302ec0f5e976cd6776e1a41da7413b0d9db</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================


================================================================================
EMAIL LOG - 2025-08-05 13:20:48
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password.php?token=881ec19cd955d4dcaea2bd0be2f5248f5e2eabb4128486b80e7737027d2fb9ee
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password.php?token=881ec19cd955d4dcaea2bd0be2f5248f5e2eabb4128486b80e7737027d2fb9ee' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password.php?token=881ec19cd955d4dcaea2bd0be2f5248f5e2eabb4128486b80e7737027d2fb9ee</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================


================================================================================
EMAIL LOG - 2025-08-05 13:21:18
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password-simple.php?token=91b40ee85b981f0c0e431ddf5c02a49163a971ceb1dbf49f31742101e694896e
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password-simple.php?token=91b40ee85b981f0c0e431ddf5c02a49163a971ceb1dbf49f31742101e694896e' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password-simple.php?token=91b40ee85b981f0c0e431ddf5c02a49163a971ceb1dbf49f31742101e694896e</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================


================================================================================
EMAIL LOG - 2025-08-05 13:21:35
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password-simple.php?token=63993c7abbe17617738917c1682fd1d5d548c8a7692e4b1693920cbc82a9003e
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password-simple.php?token=63993c7abbe17617738917c1682fd1d5d548c8a7692e4b1693920cbc82a9003e' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password-simple.php?token=63993c7abbe17617738917c1682fd1d5d548c8a7692e4b1693920cbc82a9003e</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================


================================================================================
EMAIL LOG - 2025-08-05 13:25:32
================================================================================
To: <EMAIL>
Subject: Password Reset Request - AI Graph Search
Reset Link: http://localhost/linked/admin/reset-password-simple.php?token=6e4572ebaf14446f7de327ceff3f94c35c7783fc2e71a78ca0995d805a412c15
--------------------------------------------------------------------------------
Message:

    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: #667eea; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; background: #f9f9f9; }
            .button { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }
            .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello testuser,</p>
                <p>You have requested to reset your password for your AI Graph Search account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center; margin: 30px 0;'>
                    <a href='http://localhost/linked/admin/reset-password-simple.php?token=6e4572ebaf14446f7de327ceff3f94c35c7783fc2e71a78ca0995d805a412c15' class='button'>Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <p style='word-break: break-all; background: #eee; padding: 10px; border-radius: 3px;'>http://localhost/linked/admin/reset-password-simple.php?token=6e4572ebaf14446f7de327ceff3f94c35c7783fc2e71a78ca0995d805a412c15</p>
                <p><strong>This link will expire in 1 hour.</strong></p>
                <p>If you did not request this password reset, please ignore this email.</p>
            </div>
            <div class='footer'>
                <p>&copy; 2025 AI Graph Search. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
================================================================================

