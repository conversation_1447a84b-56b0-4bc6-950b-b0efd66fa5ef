<?php
/**
 * Admin System Setup Script
 * 
 * Initializes the database, creates tables, and sets up
 * the default admin account for first-time installation.
 */

// Prevent running in production
if (defined('ENVIRONMENT') && ENVIRONMENT === 'production') {
    die('Setup script cannot be run in production environment.');
}

require_once __DIR__ . '/bootstrap.php';

$error = '';
$success = '';
$setupComplete = false;

// Check if setup is already complete
try {
    $db = Database::getInstance();
    $adminExists = $db->fetchColumn("SELECT COUNT(*) FROM admin_users WHERE role = 'superadmin'");
    if ($adminExists > 0) {
        $setupComplete = true;
    }
} catch (Exception $e) {
    // Database might not exist yet, continue with setup
}

// Handle setup form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$setupComplete) {
    try {
        $adminUsername = sanitizeInput($_POST['admin_username'] ?? '');
        $adminEmail = sanitizeInput($_POST['admin_email'] ?? '');
        $adminPassword = $_POST['admin_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        // Validation
        if (empty($adminUsername) || empty($adminEmail) || empty($adminPassword)) {
            throw new Exception('All fields are required.');
        }
        
        if (!isValidEmail($adminEmail)) {
            throw new Exception('Please enter a valid email address.');
        }
        
        if ($adminPassword !== $confirmPassword) {
            throw new Exception('Passwords do not match.');
        }
        
        // Validate password strength
        $passwordErrors = validatePasswordStrength($adminPassword);
        if (!empty($passwordErrors)) {
            throw new Exception(implode(' ', $passwordErrors));
        }
        
        // Run database setup
        $setupSql = file_get_contents(__DIR__ . '/database/setup.sql');
        if (!$setupSql) {
            throw new Exception('Could not read database setup file.');
        }
        
        // Execute setup SQL
        $db = Database::getInstance();
        $pdo = $db->getPDO();
        
        // Split SQL into individual statements
        $statements = array_filter(array_map('trim', explode(';', $setupSql)));
        
        $pdo->beginTransaction();
        try {
            foreach ($statements as $statement) {
                if (!empty($statement) && !preg_match('/^(--|\/\*)/', $statement)) {
                    $pdo->exec($statement);
                }
            }
            
            // Create the admin user
            $passwordHash = password_hash($adminPassword, PASSWORD_DEFAULT);
            
            // Remove the default admin first
            $pdo->exec("DELETE FROM admin_users WHERE username = 'admin'");
            
            // Insert the new admin
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, role, is_active) 
                VALUES (?, ?, ?, 'superadmin', 1)
            ");
            $stmt->execute([$adminUsername, $adminEmail, $passwordHash]);
            
            $pdo->commit();
            
            $success = 'Admin system setup completed successfully! You can now log in with your credentials.';
            $setupComplete = true;
            
        } catch (Exception $e) {
            $pdo->rollback();
            throw $e;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin System Setup - <?php echo getConfig('app', 'app_name'); ?></title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .setup-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body class="setup-container">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="setup-card rounded-lg shadow-2xl p-8 w-full max-w-md">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="mx-auto w-16 h-16 bg-purple-600 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-cog text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Admin System Setup</h1>
                <p class="text-gray-600 mt-2"><?php echo getConfig('app', 'app_name'); ?></p>
            </div>

            <?php if ($setupComplete): ?>
                <!-- Setup Complete -->
                <div class="text-center">
                    <div class="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                        <i class="fas fa-check text-white text-2xl"></i>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800 mb-4">Setup Complete!</h2>
                    
                    <?php if ($success): ?>
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                            <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success); ?>
                        </div>
                    <?php endif; ?>
                    
                    <p class="text-gray-600 mb-6">
                        The admin system has been successfully configured. You can now access the admin panel.
                    </p>
                    
                    <a href="login.php" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 inline-block text-center">
                        <i class="fas fa-sign-in-alt mr-2"></i>Go to Login
                    </a>
                    
                    <div class="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h3 class="text-yellow-800 font-bold text-sm mb-2">
                            <i class="fas fa-exclamation-triangle mr-2"></i>Security Notice
                        </h3>
                        <p class="text-yellow-700 text-xs">
                            For security reasons, please delete or rename this setup.php file after completing the installation.
                        </p>
                    </div>
                </div>
                
            <?php else: ?>
                <!-- Setup Form -->
                <?php if ($error): ?>
                    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                        <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?>
                    </div>
                <?php endif; ?>

                <form method="POST" id="setupForm">
                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Admin Username
                        </label>
                        <input type="text" name="admin_username" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter admin username" required
                               value="<?php echo htmlspecialchars($_POST['admin_username'] ?? ''); ?>">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Admin Email
                        </label>
                        <input type="email" name="admin_email" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter admin email" required
                               value="<?php echo htmlspecialchars($_POST['admin_email'] ?? ''); ?>">
                    </div>

                    <div class="mb-4">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Admin Password
                        </label>
                        <input type="password" name="admin_password" id="adminPassword"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter admin password" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-gray-700 text-sm font-bold mb-2">
                            Confirm Password
                        </label>
                        <input type="password" name="confirm_password" id="confirmPassword"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Confirm admin password" required>
                        <p class="text-xs text-gray-500 mt-1" id="matchText"></p>
                    </div>

                    <!-- Password Requirements -->
                    <div class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                        <h3 class="text-gray-800 font-bold text-sm mb-2">Password Requirements:</h3>
                        <ul class="text-gray-600 text-xs space-y-1">
                            <li>• At least <?php echo getConfig('security', 'password_min_length'); ?> characters</li>
                            <li>• At least one uppercase letter</li>
                            <li>• At least one lowercase letter</li>
                            <li>• At least one number</li>
                            <li>• At least one special character</li>
                        </ul>
                    </div>

                    <button type="submit" id="submitBtn" class="w-full bg-purple-600 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-rocket mr-2"></i>Setup Admin System
                    </button>
                </form>

                <!-- System Information -->
                <div class="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h3 class="text-blue-800 font-bold text-sm mb-2">
                        <i class="fas fa-info-circle mr-2"></i>System Information
                    </h3>
                    <ul class="text-blue-700 text-xs space-y-1">
                        <li>• Database: <?php echo DB_HOST . ':' . DB_PORT . '/' . DB_NAME; ?></li>
                        <li>• Environment: <?php echo ENVIRONMENT; ?></li>
                        <li>• Version: <?php echo getConfig('app', 'app_version'); ?></li>
                        <li>• PHP Version: <?php echo PHP_VERSION; ?></li>
                    </ul>
                </div>
            <?php endif; ?>

            <!-- Footer -->
            <div class="mt-8 text-center text-xs text-gray-500">
                <p>&copy; <?php echo date('Y'); ?> <?php echo getConfig('app', 'app_name'); ?></p>
            </div>
        </div>
    </div>

    <script>
        const adminPasswordInput = document.getElementById('adminPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const matchText = document.getElementById('matchText');
        const submitBtn = document.getElementById('submitBtn');

        function checkPasswordMatch() {
            const password = adminPasswordInput?.value || '';
            const confirm = confirmPasswordInput?.value || '';

            if (confirm === '') {
                matchText.textContent = '';
                matchText.className = 'text-xs text-gray-500 mt-1';
                return false;
            }

            if (password === confirm) {
                matchText.textContent = '✓ Passwords match';
                matchText.className = 'text-xs text-green-600 mt-1';
                return true;
            } else {
                matchText.textContent = '✗ Passwords do not match';
                matchText.className = 'text-xs text-red-600 mt-1';
                return false;
            }
        }

        function updateSubmitButton() {
            const isMatch = checkPasswordMatch();
            const hasPassword = adminPasswordInput?.value.length > 0;
            
            if (submitBtn) {
                submitBtn.disabled = !(isMatch && hasPassword);
            }
        }

        if (adminPasswordInput && confirmPasswordInput) {
            adminPasswordInput.addEventListener('input', updateSubmitButton);
            confirmPasswordInput.addEventListener('input', updateSubmitButton);
            updateSubmitButton();
        }
    </script>
</body>
</html>
