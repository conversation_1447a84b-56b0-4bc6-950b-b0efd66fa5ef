<?php
/**
 * Modern Admin Dashboard
 *
 * Beautiful, responsive dashboard with enhanced UX
 */

// Load configuration first (before session_start)
define('ADMIN_SYSTEM_LOADED', true);
require_once __DIR__ . '/config/config.php';

session_start();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'Unknown';
$email = $_SESSION['admin_email'] ?? '<EMAIL>';
$role = $_SESSION['admin_role'] ?? 'user';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Graph Search Admin System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.6s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .glass-effect {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.15);
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(59, 130, 246, 0.25);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="md:hidden mr-4 text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                        Graph Search Admin System
                    </h1>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($username); ?></p>
                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($role); ?></p>
                        </div>
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm"><?php echo strtoupper(substr($username, 0, 1)); ?></span>
                        </div>
                    </div>

                    <!-- Logout -->
                    <a href="login.php" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-white w-64 shadow-lg">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg">
                        <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                    </a>
                    <a href="graphsearch.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-key mr-3"></i>License Management
                        <span class="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">NEW</span>
                    </a>
                    <a href="../graphsearch.html" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-search mr-3"></i>Launch Search
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-chart-bar mr-3"></i>Analytics
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-cog mr-3"></i>Settings
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Welcome Section -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-2">
                    Welcome back, <?php echo htmlspecialchars($username); ?>! 👋
                </h2>
                <p class="text-gray-600">
                    Here's what's happening with your Graph Search system today.
                </p>
            </div>

            <!-- License Stats -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-key"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Licenses</p>
                            <p id="totalLicenses" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Active Licenses</p>
                            <p id="activeLicenses" class="text-2xl font-bold text-gray-900">0</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Revenue (Monthly)</p>
                            <p id="monthlyRevenue" class="text-2xl font-bold text-gray-900">$0</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
                    <div class="space-y-3">
                        <a href="graphsearch.php" class="block w-full text-left px-4 py-3 bg-purple-600 text-white rounded hover:bg-purple-700 transition-colors">
                            <i class="fas fa-key mr-2"></i>Launch License Management
                        </a>
                        <a href="../graphsearch.html" class="block w-full text-left px-4 py-3 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                            <i class="fas fa-search mr-2"></i>Launch Search
                        </a>
                        <a href="#" class="block w-full text-left px-4 py-3 bg-green-600 text-white rounded hover:bg-green-700 transition-colors">
                            <i class="fas fa-cog mr-2"></i>System Configuration
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">System Information</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">PHP Version</span>
                            <span class="text-sm text-green-600"><?php echo PHP_VERSION; ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Session Status</span>
                            <span class="text-sm text-green-600">Active</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">User Role</span>
                            <span class="text-sm text-blue-600"><?php echo htmlspecialchars($role); ?></span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Login Time</span>
                            <span class="text-sm text-gray-600"><?php echo date('H:i:s'); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integration Status -->
            <div class="mt-8 bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">System Status</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">✅ Active Services</h4>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Admin authentication system</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>License management system</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Database connectivity</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Security configuration</li>
                            <li><i class="fas fa-check text-green-500 mr-2"></i>Dashboard interface</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-medium text-gray-700 mb-3">🔧 Available Features</h4>
                        <ul class="space-y-2 text-sm text-gray-600">
                            <li><i class="fas fa-arrow-right text-blue-500 mr-2"></i>Generate new licenses</li>
                            <li><i class="fas fa-arrow-right text-blue-500 mr-2"></i>Manage existing licenses</li>
                            <li><i class="fas fa-arrow-right text-blue-500 mr-2"></i>View license statistics</li>
                            <li><i class="fas fa-arrow-right text-blue-500 mr-2"></i>Export license data</li>
                            <li><i class="fas fa-arrow-right text-blue-500 mr-2"></i>Monitor system health</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('hidden');
        });

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Modern Dashboard loaded successfully!');

            // Load license statistics
            loadLicenseStats();
        });

        // Load license statistics from the license management system
        function loadLicenseStats() {
            $.ajax({
                url: 'graphsearch.php',
                method: 'POST',
                data: { action: 'get_stats' },
                dataType: 'json',
                success: function(response) {
                    if (response.success && response.stats) {
                        const stats = response.stats;
                        $('#totalLicenses').text(stats.total || 0);
                        $('#activeLicenses').text(stats.active || 0);

                        // Calculate revenue (assuming basic pricing)
                        const revenue = (stats.active || 0) * 29; // $29 per active license
                        $('#monthlyRevenue').text('$' + revenue.toLocaleString());
                    }
                },
                error: function() {
                    console.error('Failed to load license statistics');
                }
            });
        }
    </script>
</body>
</html>