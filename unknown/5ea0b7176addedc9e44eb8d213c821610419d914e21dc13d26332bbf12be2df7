<?php
/**
 * Simple Database Setup
 * 
 * Creates the database and tables without complex dependencies
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

$error = '';
$success = '';
$step = 1;

// Database configuration
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'graphDB';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        if (isset($_POST['create_database'])) {
            // Step 1: Create database
            $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create database
            $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
            $success = "Database '$dbName' created successfully!";
            $step = 2;
            
        } elseif (isset($_POST['create_tables'])) {
            // Step 2: Create tables
            $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Create admin_users table
            $sql = "
            CREATE TABLE IF NOT EXISTS admin_users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                role ENUM('superadmin', 'admin', 'moderator') DEFAULT 'admin',
                is_active BOOLEAN DEFAULT TRUE,
                2fa_enabled BOOLEAN DEFAULT FALSE,
                2fa_secret VARCHAR(32) NULL,
                failed_attempts INT DEFAULT 0,
                locked_until TIMESTAMP NULL,
                last_login TIMESTAMP NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $pdo->exec($sql);
            
            // Create other tables
            $tables = [
                "CREATE TABLE IF NOT EXISTS password_reset_tokens (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    token VARCHAR(64) NOT NULL,
                    expires_at DATETIME NOT NULL,
                    used BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
                )",

                "CREATE TABLE IF NOT EXISTS login_sessions (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NOT NULL,
                    session_id VARCHAR(128) NOT NULL,
                    ip_address VARCHAR(45) NOT NULL,
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    expires_at DATETIME NOT NULL,
                    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
                )",
                
                "CREATE TABLE IF NOT EXISTS login_attempts (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    username VARCHAR(50),
                    ip_address VARCHAR(45) NOT NULL,
                    success BOOLEAN NOT NULL,
                    failure_reason VARCHAR(255),
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_username (username),
                    INDEX idx_ip_address (ip_address),
                    INDEX idx_created_at (created_at)
                )",
                
                "CREATE TABLE IF NOT EXISTS admin_activity_log (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT,
                    action VARCHAR(100) NOT NULL,
                    description TEXT,
                    ip_address VARCHAR(45),
                    user_agent TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL
                )"
            ];
            
            foreach ($tables as $tableSql) {
                $pdo->exec($tableSql);
            }
            
            $success = "All database tables created successfully!";
            $step = 3;
            
        } elseif (isset($_POST['create_admin'])) {
            // Step 3: Create admin user
            $username = trim($_POST['admin_username']);
            $email = trim($_POST['admin_email']);
            $password = $_POST['admin_password'];
            
            if (empty($username) || empty($email) || empty($password)) {
                throw new Exception('All fields are required.');
            }
            
            if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Please enter a valid email address.');
            }
            
            if (strlen($password) < 8) {
                throw new Exception('Password must be at least 8 characters long.');
            }
            
            $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            // Check if admin already exists
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM admin_users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetchColumn() > 0) {
                throw new Exception('Username or email already exists.');
            }
            
            // Create admin user
            $passwordHash = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("
                INSERT INTO admin_users (username, email, password_hash, role, is_active) 
                VALUES (?, ?, ?, 'superadmin', 1)
            ");
            $stmt->execute([$username, $email, $passwordHash]);
            
            $success = "Admin user '$username' created successfully! You can now log in.";
            $step = 4;
        }
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// Check current status
try {
    // Check if database exists
    $pdo = new PDO("mysql:host=$dbHost", $dbUser, $dbPass);
    $stmt = $pdo->query("SHOW DATABASES LIKE '$dbName'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        $step = 2;
        
        // Check if tables exist
        $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
        $stmt = $pdo->query("SHOW TABLES LIKE 'admin_users'");
        $tablesExist = $stmt->rowCount() > 0;
        
        if ($tablesExist) {
            $step = 3;
            
            // Check if admin user exists
            $stmt = $pdo->query("SELECT COUNT(*) FROM admin_users WHERE role = 'superadmin'");
            $adminExists = $stmt->fetchColumn() > 0;
            
            if ($adminExists) {
                $step = 4;
            }
        }
    }
} catch (Exception $e) {
    // Database connection failed, start from step 1
    $step = 1;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Database Setup</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-md w-full bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Database Setup</h1>
                <p class="text-gray-600">Step <?php echo $step; ?> of 4</p>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <?php if ($step == 1): ?>
                <!-- Step 1: Create Database -->
                <div class="text-center">
                    <i class="fas fa-database text-6xl text-blue-500 mb-4"></i>
                    <h2 class="text-xl font-semibold mb-4">Create Database</h2>
                    <p class="text-gray-600 mb-6">Create the GraphDB database for the admin system.</p>
                    <form method="POST">
                        <button type="submit" name="create_database" class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-plus mr-2"></i>Create Database
                        </button>
                    </form>
                </div>

            <?php elseif ($step == 2): ?>
                <!-- Step 2: Create Tables -->
                <div class="text-center">
                    <i class="fas fa-table text-6xl text-green-500 mb-4"></i>
                    <h2 class="text-xl font-semibold mb-4">Create Tables</h2>
                    <p class="text-gray-600 mb-6">Create the required database tables for the admin system.</p>
                    <form method="POST">
                        <button type="submit" name="create_tables" class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700">
                            <i class="fas fa-plus mr-2"></i>Create Tables
                        </button>
                    </form>
                </div>

            <?php elseif ($step == 3): ?>
                <!-- Step 3: Create Admin User -->
                <div>
                    <div class="text-center mb-6">
                        <i class="fas fa-user-shield text-6xl text-purple-500 mb-4"></i>
                        <h2 class="text-xl font-semibold mb-2">Create Admin User</h2>
                        <p class="text-gray-600">Create your administrator account.</p>
                    </div>
                    
                    <form method="POST">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Username</label>
                            <input type="text" name="admin_username" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                                   placeholder="Enter admin username">
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Email</label>
                            <input type="email" name="admin_email" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                                   placeholder="Enter admin email">
                        </div>
                        
                        <div class="mb-6">
                            <label class="block text-gray-700 text-sm font-bold mb-2">Password</label>
                            <input type="password" name="admin_password" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                                   placeholder="Enter admin password">
                            <p class="text-xs text-gray-500 mt-1">Minimum 8 characters</p>
                        </div>
                        
                        <button type="submit" name="create_admin" class="w-full bg-purple-600 text-white py-3 px-4 rounded-lg hover:bg-purple-700">
                            <i class="fas fa-user-plus mr-2"></i>Create Admin User
                        </button>
                    </form>
                </div>

            <?php else: ?>
                <!-- Step 4: Complete -->
                <div class="text-center">
                    <i class="fas fa-check-circle text-6xl text-green-500 mb-4"></i>
                    <h2 class="text-xl font-semibold mb-4">Setup Complete!</h2>
                    <p class="text-gray-600 mb-6">Your admin system is ready to use.</p>
                    
                    <div class="space-y-3">
                        <a href="login_simple.php" class="block w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700">
                            <i class="fas fa-sign-in-alt mr-2"></i>Go to Login
                        </a>
                        <a href="dashboard_working.php" class="block w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700">
                            <i class="fas fa-tachometer-alt mr-2"></i>Go to Dashboard
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Navigation -->
            <div class="mt-8 text-center">
                <a href="minimal_test.php" class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Test Page
                </a>
            </div>
        </div>
    </div>
</body>
</html>
