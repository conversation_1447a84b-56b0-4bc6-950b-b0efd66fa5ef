-- GraphDB Admin Authentication System Database Setup
-- Run this script in phpMyAdmin or MySQL command line

-- Create database
CREATE DATABASE IF NOT EXISTS `graphDB` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE `graphDB`;

-- Admin users table
CREATE TABLE IF NOT EXISTS `admin_users` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `username` VARCHAR(50) NOT NULL UNIQUE,
    `email` VARCHAR(255) NOT NULL UNIQUE,
    `password_hash` VARCHAR(255) NOT NULL,
    `2fa_secret` VARCHAR(32) DEFAULT NULL,
    `2fa_enabled` TINYINT(1) DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `last_login` TIM<PERSON><PERSON>MP NULL DEFAULT NULL,
    `failed_attempts` INT(11) DEFAULT 0,
    `locked_until` TIMESTAMP NULL DEFAULT NULL,
    `is_active` TINYINT(1) DEFAULT 1,
    `role` ENUM('superadmin', 'admin', 'moderator') DEFAULT 'admin',
    PRIMARY KEY (`id`),
    INDEX `idx_username` (`username`),
    INDEX `idx_email` (`email`),
    INDEX `idx_locked_until` (`locked_until`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Password reset tokens table
CREATE TABLE IF NOT EXISTS `password_reset_tokens` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `token` VARCHAR(64) NOT NULL UNIQUE,
    `email` VARCHAR(255) NOT NULL,
    `admin_id` INT(11) UNSIGNED NOT NULL,
    `expires_at` TIMESTAMP NOT NULL,
    `used` TINYINT(1) DEFAULT 0,
    `used_at` TIMESTAMP NULL DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `ip_address` VARCHAR(45) DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_token` (`token`),
    INDEX `idx_email` (`email`),
    INDEX `idx_expires_at` (`expires_at`),
    FOREIGN KEY (`admin_id`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Login sessions table
CREATE TABLE IF NOT EXISTS `login_sessions` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `session_id` VARCHAR(128) NOT NULL UNIQUE,
    `admin_id` INT(11) UNSIGNED NOT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NOT NULL,
    `last_activity` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `is_active` TINYINT(1) DEFAULT 1,
    `remember_me` TINYINT(1) DEFAULT 0,
    PRIMARY KEY (`id`),
    INDEX `idx_session_id` (`session_id`),
    INDEX `idx_admin_id` (`admin_id`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_ip_address` (`ip_address`),
    FOREIGN KEY (`admin_id`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Login attempts table (for rate limiting and security monitoring)
CREATE TABLE IF NOT EXISTS `login_attempts` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `ip_address` VARCHAR(45) NOT NULL,
    `username` VARCHAR(50) DEFAULT NULL,
    `success` TINYINT(1) NOT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `failure_reason` VARCHAR(100) DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_ip_address` (`ip_address`),
    INDEX `idx_username` (`username`),
    INDEX `idx_created_at` (`created_at`),
    INDEX `idx_success` (`success`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Admin activity log table
CREATE TABLE IF NOT EXISTS `admin_activity_log` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `admin_id` INT(11) UNSIGNED NOT NULL,
    `action` VARCHAR(100) NOT NULL,
    `description` TEXT DEFAULT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `metadata` JSON DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_admin_id` (`admin_id`),
    INDEX `idx_action` (`action`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`admin_id`) REFERENCES `admin_users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Licenses table for GraphSearch license management
CREATE TABLE IF NOT EXISTS `licenses` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `license_key` VARCHAR(255) NOT NULL UNIQUE,
    `customer_name` VARCHAR(255) NOT NULL,
    `customer_email` VARCHAR(255) NOT NULL,
    `license_type` ENUM('basic', 'professional', 'enterprise') NOT NULL DEFAULT 'basic',
    `status` ENUM('active', 'suspended', 'expired') DEFAULT 'active',
    `max_devices` INT(11) DEFAULT 3,
    `duration_months` INT(11) NOT NULL DEFAULT 12,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `expires_at` TIMESTAMP NOT NULL,
    `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `notes` TEXT DEFAULT NULL,
    `created_by` INT(11) UNSIGNED DEFAULT NULL,
    `total_searches` INT(11) DEFAULT 0,
    `last_used` TIMESTAMP NULL DEFAULT NULL,
    `license_data` JSON DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_license_key` (`license_key`),
    INDEX `idx_customer_email` (`customer_email`),
    INDEX `idx_status` (`status`),
    INDEX `idx_expires_at` (`expires_at`),
    INDEX `idx_created_by` (`created_by`),
    INDEX `idx_license_type` (`license_type`),
    FOREIGN KEY (`created_by`) REFERENCES `admin_users`(`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- License usage tracking table
CREATE TABLE IF NOT EXISTS `license_usage` (
    `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
    `license_id` INT(11) UNSIGNED NOT NULL,
    `device_fingerprint` VARCHAR(255) DEFAULT NULL,
    `ip_address` VARCHAR(45) NOT NULL,
    `user_agent` TEXT DEFAULT NULL,
    `search_query` TEXT DEFAULT NULL,
    `results_count` INT(11) DEFAULT 0,
    `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    `session_data` JSON DEFAULT NULL,
    PRIMARY KEY (`id`),
    INDEX `idx_license_id` (`license_id`),
    INDEX `idx_device_fingerprint` (`device_fingerprint`),
    INDEX `idx_ip_address` (`ip_address`),
    INDEX `idx_created_at` (`created_at`),
    FOREIGN KEY (`license_id`) REFERENCES `licenses`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default superadmin account
-- Password: AdminPass123! (will be hashed)
INSERT INTO `admin_users` (`username`, `email`, `password_hash`, `role`, `is_active`) VALUES 
('admin', '<EMAIL>', '$2y$12$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'superadmin', 1);

-- Create cleanup procedure for expired tokens and sessions
DELIMITER //
CREATE PROCEDURE CleanupExpiredData()
BEGIN
    -- Clean up expired password reset tokens
    DELETE FROM `password_reset_tokens` WHERE `expires_at` < NOW();

    -- Clean up expired sessions
    DELETE FROM `login_sessions` WHERE `expires_at` < NOW();

    -- Clean up old login attempts (keep last 30 days)
    DELETE FROM `login_attempts` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 30 DAY);

    -- Clean up old activity logs (keep last 90 days)
    DELETE FROM `admin_activity_log` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 90 DAY);

    -- Update expired licenses status
    UPDATE `licenses` SET `status` = 'expired' WHERE `expires_at` < NOW() AND `status` = 'active';

    -- Clean up old license usage data (keep last 180 days)
    DELETE FROM `license_usage` WHERE `created_at` < DATE_SUB(NOW(), INTERVAL 180 DAY);
END //
DELIMITER ;

-- Create event to run cleanup daily
CREATE EVENT IF NOT EXISTS `daily_cleanup`
ON SCHEDULE EVERY 1 DAY
STARTS CURRENT_TIMESTAMP
DO CALL CleanupExpiredData();

-- Enable event scheduler
SET GLOBAL event_scheduler = ON;
