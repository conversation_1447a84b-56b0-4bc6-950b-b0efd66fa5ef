<?php
/**
 * Security Logger Class
 * 
 * Handles security event logging, activity tracking, and audit trails
 * for the admin authentication system.
 */

class SecurityLogger {
    private $db;
    private $logFile;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logFile = getConfig('app', 'log_file');

        // Ensure log directory exists
        if ($this->logFile) {
            $logDir = dirname($this->logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
        } else {
            // Fallback log file
            $this->logFile = __DIR__ . '/../logs/admin.log';
            $logDir = dirname($this->logFile);
            if (!is_dir($logDir)) {
                mkdir($logDir, 0755, true);
            }
        }
    }
    
    /**
     * Log successful login
     */
    public function logSuccessfulLogin($userId, $ip) {
        $this->logActivity($userId, 'login_success', 'User logged in successfully', $ip);
        $this->writeToFile('INFO', "Successful login for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log failed login attempt
     */
    public function logFailedLogin($username, $ip, $reason) {
        $this->writeToFile('WARNING', "Failed login attempt for username: $username from IP: $ip. Reason: $reason");
        
        // Also log to database if we can identify the user
        $user = $this->findUserByUsername($username);
        if ($user) {
            $this->logActivity($user['id'], 'login_failed', "Failed login attempt: $reason", $ip);
        }
    }
    
    /**
     * Log logout
     */
    public function logLogout($userId, $ip) {
        $this->logActivity($userId, 'logout', 'User logged out', $ip);
        $this->writeToFile('INFO', "User logout for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log emergency login
     */
    public function logEmergencyLogin($ip) {
        $this->writeToFile('CRITICAL', "Emergency superadmin login from IP: $ip");
        
        // Log to activity table with special user ID 0 for emergency
        $this->logActivity(0, 'emergency_login', 'Emergency superadmin access used', $ip, [
            'emergency' => true,
            'requires_investigation' => true
        ]);
    }
    
    /**
     * Log password change
     */
    public function logPasswordChange($userId, $ip) {
        $this->logActivity($userId, 'password_change', 'Password changed', $ip);
        $this->writeToFile('INFO', "Password changed for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log account lockout
     */
    public function logAccountLock($userId, $ip, $failedAttempts) {
        $this->logActivity($userId, 'account_locked', "Account locked after $failedAttempts failed attempts", $ip, [
            'failed_attempts' => $failedAttempts
        ]);
        $this->writeToFile('WARNING', "Account locked for user ID: $userId from IP: $ip after $failedAttempts failed attempts");
    }
    
    /**
     * Log account unlock
     */
    public function logAccountUnlock($userId, $unlockedBy, $ip) {
        $this->logActivity($userId, 'account_unlocked', "Account unlocked by admin", $ip, [
            'unlocked_by' => $unlockedBy
        ]);
        $this->writeToFile('INFO', "Account unlocked for user ID: $userId by admin ID: $unlockedBy from IP: $ip");
    }
    
    /**
     * Log 2FA enabled
     */
    public function logTwoFactorEnabled($userId, $ip) {
        $this->logActivity($userId, '2fa_enabled', 'Two-factor authentication enabled', $ip);
        $this->writeToFile('INFO', "2FA enabled for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log 2FA disabled
     */
    public function logTwoFactorDisabled($userId, $ip) {
        $this->logActivity($userId, '2fa_disabled', 'Two-factor authentication disabled', $ip);
        $this->writeToFile('WARNING', "2FA disabled for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log password reset request
     */
    public function logPasswordResetRequest($email, $ip) {
        $this->writeToFile('INFO', "Password reset requested for email: $email from IP: $ip");
        
        $user = $this->findUserByEmail($email);
        if ($user) {
            $this->logActivity($user['id'], 'password_reset_request', 'Password reset requested', $ip);
        }
    }
    
    /**
     * Log password reset completion
     */
    public function logPasswordResetComplete($userId, $ip) {
        $this->logActivity($userId, 'password_reset_complete', 'Password reset completed', $ip);
        $this->writeToFile('INFO', "Password reset completed for user ID: $userId from IP: $ip");
    }
    
    /**
     * Log suspicious activity
     */
    public function logSuspiciousActivity($userId, $ip, $description) {
        $this->logActivity($userId, 'suspicious_activity', $description, $ip, [
            'requires_investigation' => true
        ]);
        $this->writeToFile('WARNING', "Suspicious activity for user ID: $userId from IP: $ip - $description");
    }
    
    /**
     * Log CSRF token validation failure
     */
    public function logCSRFFailure($ip, $action) {
        $this->writeToFile('WARNING', "CSRF token validation failed for action: $action from IP: $ip");
    }
    
    /**
     * Log rate limit exceeded
     */
    public function logRateLimitExceeded($ip, $action) {
        $this->writeToFile('WARNING', "Rate limit exceeded for action: $action from IP: $ip");
    }
    
    /**
     * Log admin action
     */
    public function logAdminAction($userId, $action, $description, $ip, $metadata = []) {
        $this->logActivity($userId, $action, $description, $ip, $metadata);
        $this->writeToFile('INFO', "Admin action by user ID: $userId from IP: $ip - $action: $description");
    }
    
    /**
     * Log to activity table
     */
    private function logActivity($userId, $action, $description, $ip, $metadata = []) {
        try {
            $this->db->insert('admin_activity_log', [
                'admin_id' => $userId,
                'action' => $action,
                'description' => $description,
                'ip_address' => $ip,
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
                'metadata' => !empty($metadata) ? json_encode($metadata) : null
            ]);
        } catch (Exception $e) {
            $this->writeToFile('ERROR', "Failed to log activity to database: " . $e->getMessage());
        }
    }
    
    /**
     * Write to log file
     */
    private function writeToFile($level, $message) {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown';
        
        $logEntry = sprintf(
            "[%s] [%s] [IP: %s] %s [User-Agent: %s]%s",
            $timestamp,
            $level,
            $ip,
            $message,
            $userAgent,
            PHP_EOL
        );
        
        // Rotate log file if it gets too large
        $this->rotateLogIfNeeded();
        
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Rotate log file if it exceeds maximum size
     */
    private function rotateLogIfNeeded() {
        if (!file_exists($this->logFile)) {
            return;
        }
        
        $maxSize = getConfig('app', 'max_log_size', 10485760); // 10MB default
        
        if (filesize($this->logFile) > $maxSize) {
            $backupFile = $this->logFile . '.' . date('Y-m-d-H-i-s') . '.bak';
            rename($this->logFile, $backupFile);
            
            // Keep only last 5 backup files
            $this->cleanupOldLogFiles();
        }
    }
    
    /**
     * Clean up old log backup files
     */
    private function cleanupOldLogFiles() {
        $logDir = dirname($this->logFile);
        $logBasename = basename($this->logFile);
        
        $backupFiles = glob($logDir . '/' . $logBasename . '.*.bak');
        
        if (count($backupFiles) > 5) {
            // Sort by modification time (oldest first)
            usort($backupFiles, function($a, $b) {
                return filemtime($a) - filemtime($b);
            });
            
            // Remove oldest files, keep only 5 most recent
            $filesToRemove = array_slice($backupFiles, 0, count($backupFiles) - 5);
            foreach ($filesToRemove as $file) {
                unlink($file);
            }
        }
    }
    
    /**
     * Get recent security events
     */
    public function getRecentEvents($limit = 50, $userId = null) {
        $sql = "SELECT * FROM admin_activity_log";
        $params = [];
        
        if ($userId) {
            $sql .= " WHERE admin_id = :user_id";
            $params[':user_id'] = $userId;
        }
        
        $sql .= " ORDER BY created_at DESC LIMIT :limit";
        $params[':limit'] = $limit;
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get failed login attempts for IP
     */
    public function getFailedLoginAttempts($ip, $timeWindow = 3600) {
        $sql = "SELECT COUNT(*) FROM login_attempts 
                WHERE ip_address = :ip 
                AND success = 0 
                AND created_at > DATE_SUB(NOW(), INTERVAL :window SECOND)";
        
        return $this->db->fetchColumn($sql, [
            ':ip' => $ip,
            ':window' => $timeWindow
        ]);
    }
    
    /**
     * Get security statistics
     */
    public function getSecurityStats($days = 7) {
        $stats = [];
        
        // Total login attempts
        $stats['total_logins'] = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM login_attempts WHERE created_at > DATE_SUB(NOW(), INTERVAL :days DAY)",
            [':days' => $days]
        );
        
        // Failed login attempts
        $stats['failed_logins'] = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM login_attempts WHERE success = 0 AND created_at > DATE_SUB(NOW(), INTERVAL :days DAY)",
            [':days' => $days]
        );
        
        // Unique IPs
        $stats['unique_ips'] = $this->db->fetchColumn(
            "SELECT COUNT(DISTINCT ip_address) FROM login_attempts WHERE created_at > DATE_SUB(NOW(), INTERVAL :days DAY)",
            [':days' => $days]
        );
        
        // Locked accounts
        $stats['locked_accounts'] = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM admin_users WHERE locked_until > NOW()"
        );
        
        // 2FA enabled users
        $stats['2fa_enabled_users'] = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM admin_users WHERE 2fa_enabled = 1 AND is_active = 1"
        );
        
        return $stats;
    }
    
    /**
     * Find user by username
     */
    private function findUserByUsername($username) {
        return $this->db->fetchRow(
            "SELECT id, username FROM admin_users WHERE username = :username",
            [':username' => $username]
        );
    }
    
    /**
     * Find user by email
     */
    private function findUserByEmail($email) {
        return $this->db->fetchRow(
            "SELECT id, email FROM admin_users WHERE email = :email",
            [':email' => $email]
        );
    }
}
?>
