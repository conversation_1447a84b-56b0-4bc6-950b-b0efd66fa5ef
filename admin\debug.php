<?php
// Simple debug script to identify the issue
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Information</h1>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Current Directory: " . __DIR__ . "</p>";
echo "<p>File exists check:</p>";

$files_to_check = [
    'config/config.php',
    'classes/Database.php',
    'classes/Auth.php',
    'classes/TwoFactorAuth.php',
    'classes/SecurityLogger.php',
    'bootstrap.php'
];

foreach ($files_to_check as $file) {
    $path = __DIR__ . '/' . $file;
    echo "- $file: " . (file_exists($path) ? "EXISTS" : "MISSING") . "<br>";
}

echo "<h2>Testing config.php inclusion</h2>";
try {
    define('ADMIN_SYSTEM_LOADED', true);
    include __DIR__ . '/config/config.php';
    echo "✅ Config loaded successfully<br>";
    echo "App name: " . getConfig('app', 'app_name') . "<br>";
} catch (Exception $e) {
    echo "❌ Config error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error in config: " . $e->getMessage() . "<br>";
}

echo "<h2>Testing class files</h2>";
$classes = ['Database', 'Auth', 'TwoFactorAuth', 'SecurityLogger'];
foreach ($classes as $class) {
    try {
        include_once __DIR__ . "/classes/$class.php";
        echo "✅ $class.php loaded successfully<br>";
    } catch (Exception $e) {
        echo "❌ Error loading $class.php: " . $e->getMessage() . "<br>";
    } catch (Error $e) {
        echo "❌ Fatal error in $class.php: " . $e->getMessage() . "<br>";
    }
}

echo "<h2>Testing bootstrap.php</h2>";
try {
    // Reset the constant for testing
    if (defined('ADMIN_SYSTEM_LOADED')) {
        echo "ADMIN_SYSTEM_LOADED already defined<br>";
    }
    
    // Try to include bootstrap
    ob_start();
    include __DIR__ . '/bootstrap.php';
    $output = ob_get_clean();
    echo "✅ Bootstrap loaded successfully<br>";
    if ($output) {
        echo "Bootstrap output: " . htmlspecialchars($output) . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Bootstrap error: " . $e->getMessage() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error in bootstrap: " . $e->getMessage() . "<br>";
}

echo "<h2>PHP Extensions</h2>";
$required_extensions = ['pdo', 'pdo_mysql', 'openssl', 'json', 'session'];
foreach ($required_extensions as $ext) {
    echo "- $ext: " . (extension_loaded($ext) ? "LOADED" : "MISSING") . "<br>";
}

echo "<h2>Directory Permissions</h2>";
$dirs = ['logs', 'uploads', 'temp'];
foreach ($dirs as $dir) {
    $path = __DIR__ . '/' . $dir;
    if (is_dir($path)) {
        echo "- $dir: " . (is_writable($path) ? "WRITABLE" : "NOT WRITABLE") . "<br>";
    } else {
        echo "- $dir: MISSING<br>";
    }
}
?>
