<?php
/**
 * Database Repair Script
 * 
 * Fixes missing tables and database issues
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

$error = '';
$success = '';

// Database configuration
$dbHost = 'localhost';
$dbUser = 'root';
$dbPass = '';
$dbName = 'graphDB';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Create missing tables
        $tables = [
            "CREATE TABLE IF NOT EXISTS login_attempts (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50),
                ip_address VARCHAR(45) NOT NULL,
                success BOOLEAN NOT NULL,
                failure_reason VARCHAR(255),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_username (username),
                INDEX idx_ip_address (ip_address),
                INDEX idx_created_at (created_at)
            )",

            "CREATE TABLE IF NOT EXISTS login_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                session_id VARCHAR(128) NOT NULL,
                ip_address VARCHAR(45) NOT NULL,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_at DATETIME NOT NULL,
                FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE CASCADE
            )",

            "CREATE TABLE IF NOT EXISTS admin_activity_log (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT,
                action VARCHAR(100) NOT NULL,
                description TEXT,
                ip_address VARCHAR(45),
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES admin_users(id) ON DELETE SET NULL
            )"
        ];

        foreach ($tables as $sql) {
            $pdo->exec($sql);
        }
        
        // Create logs directory
        $logDir = __DIR__ . '/logs';
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        // Create uploads directory
        $uploadDir = __DIR__ . '/uploads';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Create temp directory
        $tempDir = __DIR__ . '/temp';
        if (!is_dir($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        $success = "Database repaired successfully! All missing tables and directories have been created.";
        
    } catch (Exception $e) {
        $error = "Repair failed: " . $e->getMessage();
    }
}

// Check current status
$status = [];
try {
    $pdo = new PDO("mysql:host=$dbHost;dbname=$dbName", $dbUser, $dbPass);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Check tables
    $tables = ['admin_users', 'password_reset_tokens', 'login_sessions', 'login_attempts', 'admin_activity_log'];
    foreach ($tables as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        $status[$table] = $stmt->rowCount() > 0;
    }
    
} catch (Exception $e) {
    $error = "Database connection failed: " . $e->getMessage();
}

// Check directories
$directories = ['logs', 'uploads', 'temp'];
foreach ($directories as $dir) {
    $status[$dir] = is_dir(__DIR__ . '/' . $dir);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Repair Tool</title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="max-w-2xl w-full bg-white rounded-lg shadow-lg p-8">
            <div class="text-center mb-8">
                <i class="fas fa-tools text-6xl text-blue-500 mb-4"></i>
                <h1 class="text-2xl font-bold text-gray-800 mb-2">Database Repair Tool</h1>
                <p class="text-gray-600">Fix missing tables and directories</p>
            </div>

            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <?php if ($success): ?>
                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-check-circle mr-2"></i><?php echo htmlspecialchars($success); ?>
                </div>
            <?php endif; ?>

            <!-- System Status -->
            <div class="mb-8">
                <h2 class="text-lg font-semibold mb-4">System Status</h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Database Tables -->
                    <div>
                        <h3 class="font-medium text-gray-700 mb-3">Database Tables</h3>
                        <div class="space-y-2">
                            <?php 
                            $tables = ['admin_users', 'password_reset_tokens', 'login_sessions', 'login_attempts', 'admin_activity_log'];
                            foreach ($tables as $table): 
                                $exists = isset($status[$table]) && $status[$table];
                            ?>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm"><?php echo $table; ?></span>
                                    <?php if ($exists): ?>
                                        <span class="text-green-600"><i class="fas fa-check"></i> Exists</span>
                                    <?php else: ?>
                                        <span class="text-red-600"><i class="fas fa-times"></i> Missing</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                    
                    <!-- Directories -->
                    <div>
                        <h3 class="font-medium text-gray-700 mb-3">Directories</h3>
                        <div class="space-y-2">
                            <?php 
                            $directories = ['logs', 'uploads', 'temp'];
                            foreach ($directories as $dir): 
                                $exists = isset($status[$dir]) && $status[$dir];
                            ?>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="text-sm"><?php echo $dir; ?>/</span>
                                    <?php if ($exists): ?>
                                        <span class="text-green-600"><i class="fas fa-check"></i> Exists</span>
                                    <?php else: ?>
                                        <span class="text-red-600"><i class="fas fa-times"></i> Missing</span>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Repair Action -->
            <div class="text-center">
                <form method="POST">
                    <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        <i class="fas fa-wrench mr-2"></i>Repair Database
                    </button>
                </form>
                
                <div class="mt-6 space-y-2">
                    <a href="login_db.php" class="block text-blue-600 hover:text-blue-800">
                        <i class="fas fa-arrow-left mr-1"></i>Back to Login
                    </a>
                    <a href="setup_simple.php" class="block text-gray-600 hover:text-gray-800">
                        <i class="fas fa-cog mr-1"></i>Database Setup
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
