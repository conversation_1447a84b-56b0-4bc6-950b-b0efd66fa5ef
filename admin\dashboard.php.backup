<?php
/**
 * Admin Dashboard
 * 
 * Main dashboard interface with security monitoring,
 * user management, and system overview.
 */

require_once __DIR__ . '/bootstrap.php';

// Require authentication
if (!$auth->isAuthenticated()) {
    redirectWithMessage('login.php', 'Please log in to access the admin dashboard.', 'error');
}

$currentUser = $auth->getCurrentUser();
$logger = new SecurityLogger();

// Handle logout
if (isset($_POST['logout'])) {
    if (validateCSRFToken($_POST['csrf_token'] ?? '')) {
        $auth->logout();
        redirectWithMessage('login.php', 'You have been logged out successfully.', 'success');
    }
}

// Get security statistics
$securityStats = $logger->getSecurityStats(7); // Last 7 days
$recentEvents = $logger->getRecentEvents(10);

// Get flash message
$flash = getFlashMessage();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - <?php echo getConfig('app', 'app_name'); ?></title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .sidebar {
            transition: transform 0.3s ease;
        }
        .sidebar.hidden {
            transform: translateX(-100%);
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="md:hidden mr-4 text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                        <?php echo getConfig('app', 'app_name'); ?>
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($currentUser['username']); ?></p>
                            <p class="text-xs text-gray-500 capitalize"><?php echo htmlspecialchars($currentUser['role']); ?></p>
                        </div>
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                    
                    <!-- Logout -->
                    <form method="POST" class="inline">
                        <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                        <button type="submit" name="logout" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-sign-out-alt"></i>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-white w-64 shadow-lg">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg">
                        <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                    </a>
                    <?php if ($auth->hasRole('admin') || $auth->hasRole('superadmin')): ?>
                    <a href="graphsearch.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-search mr-3"></i>GraphSearch Tool
                        <span class="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">NEW</span>
                    </a>
                    <?php endif; ?>
                    <a href="users.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-users mr-3"></i>User Management
                    </a>
                    <a href="security.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-shield-alt mr-3"></i>Security Logs
                    </a>
                    <a href="settings.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-cog mr-3"></i>Settings
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-user-circle mr-3"></i>Profile
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
            <!-- Flash Messages -->
            <?php if ($flash): ?>
                <div class="mb-6 bg-<?php echo $flash['type'] === 'error' ? 'red' : ($flash['type'] === 'warning' ? 'yellow' : 'green'); ?>-100 border border-<?php echo $flash['type'] === 'error' ? 'red' : ($flash['type'] === 'warning' ? 'yellow' : 'green'); ?>-400 text-<?php echo $flash['type'] === 'error' ? 'red' : ($flash['type'] === 'warning' ? 'yellow' : 'green'); ?>-700 px-4 py-3 rounded">
                    <?php echo htmlspecialchars($flash['message']); ?>
                </div>
            <?php endif; ?>

            <!-- Welcome Section -->
            <div class="mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-2">
                    Welcome back, <?php echo htmlspecialchars($currentUser['username']); ?>!
                </h2>
                <p class="text-gray-600">
                    Last login: <?php echo $currentUser['last_login'] ? formatDate($currentUser['last_login']) : 'First time login'; ?>
                </p>
                
                <?php if ($currentUser['is_emergency'] ?? false): ?>
                    <div class="mt-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        <strong>Emergency Access Mode:</strong> You are logged in with emergency credentials. This session is being monitored.
                    </div>
                <?php endif; ?>
            </div>

            <!-- Security Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Logins (7d)</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($securityStats['total_logins']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-red-100 text-red-600">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Failed Logins (7d)</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($securityStats['failed_logins']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                            <i class="fas fa-lock"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Locked Accounts</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($securityStats['locked_accounts']); ?></p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center">
                        <div class="p-3 rounded-full bg-green-100 text-green-600">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">2FA Enabled</p>
                            <p class="text-2xl font-bold text-gray-900"><?php echo number_format($securityStats['2fa_enabled_users']); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Security Events -->
            <div class="bg-white rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-medium text-gray-900">Recent Security Events</h3>
                </div>
                <div class="p-6">
                    <?php if (empty($recentEvents)): ?>
                        <p class="text-gray-500 text-center py-4">No recent security events</p>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($recentEvents as $event): ?>
                                <div class="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div class="flex-shrink-0">
                                        <?php
                                        $iconClass = 'fas fa-info-circle text-blue-600';
                                        if (strpos($event['action'], 'failed') !== false || strpos($event['action'], 'locked') !== false) {
                                            $iconClass = 'fas fa-exclamation-triangle text-red-600';
                                        } elseif (strpos($event['action'], 'login') !== false) {
                                            $iconClass = 'fas fa-sign-in-alt text-green-600';
                                        } elseif (strpos($event['action'], 'emergency') !== false) {
                                            $iconClass = 'fas fa-exclamation-triangle text-red-600';
                                        }
                                        ?>
                                        <i class="<?php echo $iconClass; ?>"></i>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900">
                                            <?php echo htmlspecialchars($event['description']); ?>
                                        </p>
                                        <p class="text-xs text-gray-500">
                                            <?php echo formatDate($event['created_at']); ?> • 
                                            IP: <?php echo htmlspecialchars($event['ip_address']); ?> •
                                            Action: <?php echo htmlspecialchars($event['action']); ?>
                                        </p>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <a href="security.php" class="text-blue-600 hover:text-blue-800 text-sm">
                                View all security logs <i class="fas fa-arrow-right ml-1"></i>
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white rounded-lg shadow p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Quick Actions</h4>
                    <div class="space-y-3">
                        <?php if ($auth->hasRole('admin') || $auth->hasRole('superadmin')): ?>
                        <a href="graphsearch.php" class="block w-full text-left px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                            <i class="fas fa-search mr-2"></i>Launch GraphSearch
                        </a>
                        <?php endif; ?>
                        <a href="users.php?action=add" class="block w-full text-left px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                            <i class="fas fa-user-plus mr-2"></i>Add New User
                        </a>
                        <a href="profile.php?section=2fa" class="block w-full text-left px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                            <i class="fas fa-shield-alt mr-2"></i>Setup 2FA
                        </a>
                        <a href="security.php" class="block w-full text-left px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700">
                            <i class="fas fa-eye mr-2"></i>View Security Logs
                        </a>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">System Status</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Database</span>
                            <span class="text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Online</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Security</span>
                            <span class="text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Active</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Backups</span>
                            <span class="text-sm text-green-600"><i class="fas fa-check-circle mr-1"></i>Current</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h4 class="text-lg font-medium text-gray-900 mb-4">Your Account</h4>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">2FA Status</span>
                            <span class="text-sm <?php echo $currentUser['2fa_enabled'] ? 'text-green-600' : 'text-red-600'; ?>">
                                <i class="fas fa-<?php echo $currentUser['2fa_enabled'] ? 'check-circle' : 'times-circle'; ?> mr-1"></i>
                                <?php echo $currentUser['2fa_enabled'] ? 'Enabled' : 'Disabled'; ?>
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-sm text-gray-600">Role</span>
                            <span class="text-sm text-blue-600 capitalize"><?php echo htmlspecialchars($currentUser['role']); ?></span>
                        </div>
                        <a href="profile.php" class="block text-center px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700">
                            <i class="fas fa-user-edit mr-2"></i>Edit Profile
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Sidebar toggle for mobile
        document.getElementById('sidebarToggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('hidden');
        });

        // Auto-hide flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessage = document.querySelector('[class*="bg-"][class*="-100"]');
            if (flashMessage) {
                setTimeout(() => {
                    flashMessage.style.transition = 'opacity 0.5s';
                    flashMessage.style.opacity = '0';
                    setTimeout(() => {
                        flashMessage.remove();
                    }, 500);
                }, 5000);
            }
        });
    </script>
</body>
</html>
