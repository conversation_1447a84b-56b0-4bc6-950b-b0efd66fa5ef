<?php
session_start();

// CORS headers for cross-origin requests
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'config/database.php';
require_once 'classes/LicenseManager.php';

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('Only POST requests are allowed');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }

    $action = $input['action'] ?? '';
    $licenseManager = new LicenseManager();

    switch ($action) {
        case 'track_device':
            $licenseKey = $input['license_key'] ?? '';
            $deviceFingerprint = $input['device_fingerprint'] ?? '';
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $searchQuery = $input['search_query'] ?? null;

            if (!$licenseKey || !$deviceFingerprint) {
                throw new Exception('License key and device fingerprint are required');
            }

            $result = $licenseManager->trackDeviceUsage($licenseKey, $deviceFingerprint, $ipAddress, $userAgent, $searchQuery);
            
            if ($result) {
                echo json_encode(['success' => true, 'message' => 'Device usage tracked']);
            } else {
                throw new Exception('Failed to track device usage');
            }
            break;

        case 'check_device_limit':
            $licenseKey = $input['license_key'] ?? '';
            $deviceFingerprint = $input['device_fingerprint'] ?? '';

            if (!$licenseKey || !$deviceFingerprint) {
                throw new Exception('License key and device fingerprint are required');
            }

            // Get license details
            $license = $licenseManager->getLicenseByKey($licenseKey);
            if (!$license) {
                throw new Exception('License not found');
            }

            // Check if license is active
            if ($license['status'] !== 'active') {
                throw new Exception('License is not active');
            }

            // Check if license has expired
            $expiryDate = new DateTime($license['expires_at']);
            $now = new DateTime();
            if ($expiryDate <= $now) {
                throw new Exception('License has expired');
            }

            // Get current device count
            $deviceCount = $licenseManager->getDeviceCount($license['id']);
            $maxDevices = $license['max_devices'];

            // Check if this device is already registered
            $sql = "SELECT COUNT(*) as count FROM license_usage 
                    WHERE license_id = :license_id 
                    AND device_fingerprint = :device_fingerprint";
            
            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute([
                'license_id' => $license['id'],
                'device_fingerprint' => $deviceFingerprint
            ]);
            
            $deviceExists = $stmt->fetch(PDO::FETCH_ASSOC)['count'] > 0;

            if (!$deviceExists && $deviceCount >= $maxDevices) {
                echo json_encode([
                    'success' => false,
                    'error' => "Device limit reached. This license allows maximum {$maxDevices} device(s). Currently {$deviceCount} devices are registered.",
                    'device_count' => $deviceCount,
                    'max_devices' => $maxDevices,
                    'device_exists' => false
                ]);
            } else {
                echo json_encode([
                    'success' => true,
                    'device_count' => $deviceCount,
                    'max_devices' => $maxDevices,
                    'device_exists' => $deviceExists,
                    'license_type' => $license['license_type'],
                    'expires_at' => $license['expires_at']
                ]);
            }
            break;

        case 'get_device_info':
            $licenseKey = $input['license_key'] ?? '';

            if (!$licenseKey) {
                throw new Exception('License key is required');
            }

            $license = $licenseManager->getLicenseByKey($licenseKey);
            if (!$license) {
                throw new Exception('License not found');
            }

            $deviceCount = $licenseManager->getDeviceCount($license['id']);
            
            // Get device details
            $sql = "SELECT device_fingerprint, ip_address, user_agent, 
                           MIN(created_at) as first_used, 
                           MAX(created_at) as last_used,
                           COUNT(*) as usage_count
                    FROM license_usage 
                    WHERE license_id = :license_id 
                    AND device_fingerprint IS NOT NULL 
                    AND device_fingerprint != ''
                    GROUP BY device_fingerprint
                    ORDER BY last_used DESC";
            
            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute(['license_id' => $license['id']]);
            
            $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'device_count' => $deviceCount,
                'max_devices' => $license['max_devices'],
                'devices' => $devices,
                'license_type' => $license['license_type'],
                'customer_email' => $license['customer_email']
            ]);
            break;

        case 'clear_device_registrations':
            $licenseKey = $input['license_key'] ?? '';

            if (!$licenseKey) {
                throw new Exception('License key is required');
            }

            $license = $licenseManager->getLicenseByKey($licenseKey);
            if (!$license) {
                throw new Exception('License not found');
            }

            // Clear all device registrations for this license
            $sql = "DELETE FROM license_usage WHERE license_id = :license_id";
            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute(['license_id' => $license['id']]);

            $deletedCount = $stmt->rowCount();

            echo json_encode([
                'success' => true,
                'message' => "Cleared {$deletedCount} device registrations",
                'deleted_count' => $deletedCount
            ]);
            break;

        case 'get_license_summary':
            $licenseKey = $input['license_key'] ?? '';

            if (!$licenseKey) {
                throw new Exception('License key is required');
            }

            $license = $licenseManager->getLicenseByKey($licenseKey);
            if (!$license) {
                throw new Exception('License not found');
            }

            $deviceCount = $licenseManager->getDeviceCount($license['id']);

            // Get recent activity
            $sql = "SELECT device_fingerprint, ip_address, user_agent, search_query, created_at
                    FROM license_usage
                    WHERE license_id = :license_id
                    ORDER BY created_at DESC
                    LIMIT 20";

            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute(['license_id' => $license['id']]);

            $recentActivity = $stmt->fetchAll(PDO::FETCH_ASSOC);

            echo json_encode([
                'success' => true,
                'license' => [
                    'id' => $license['id'],
                    'customer_name' => $license['customer_name'],
                    'customer_email' => $license['customer_email'],
                    'license_type' => $license['license_type'],
                    'status' => $license['status'],
                    'expires_at' => $license['expires_at'],
                    'max_devices' => $license['max_devices'],
                    'device_count' => $deviceCount
                ],
                'recent_activity' => $recentActivity
            ]);
            break;

        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ]);
}
?>
