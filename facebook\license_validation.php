<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Load configuration
require_once __DIR__ . '/config.php';

try {
    $pdo = getFacebookDatabase();
} catch (Exception $e) {
    logFacebookError('Database connection failed', ['error' => $e->getMessage()]);
    echo json_encode(['success' => false, 'error' => 'Database connection failed']);
    exit();
}

// Get action from request
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'validate':
        validateLicense($pdo);
        break;
    case 'activate':
        activateLicense($pdo);
        break;
    case 'check':
        checkLicenseStatus($pdo);
        break;
    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

function validateLicense($pdo) {
    $licenseKey = $_POST['license_key'] ?? '';
    $email = $_POST['email'] ?? '';
    $hardwareId = $_POST['hardware_id'] ?? '';
    
    if (!$licenseKey || !$email || !$hardwareId) {
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    try {
        // Check if license exists and is valid
        $stmt = $pdo->prepare("
            SELECT * FROM licenses 
            WHERE license_key = ? AND customer_email = ? AND status = 'active'
        ");
        $stmt->execute([$licenseKey, $email]);
        $license = $stmt->fetch();
        
        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'Invalid license key or email']);
            return;
        }
        
        // Check if license is expired
        $expiryDate = new DateTime($license['expires_at']);
        $currentDate = new DateTime();

        if ($expiryDate < $currentDate) {
            echo json_encode(['success' => false, 'error' => 'License has expired']);
            return;
        }
        
        // Check device limit
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as device_count 
            FROM license_devices 
            WHERE license_key = ?
        ");
        $stmt->execute([$licenseKey]);
        $deviceCount = $stmt->fetch()['device_count'];
        
        if ($deviceCount >= $license['max_devices']) {
            // Check if this hardware ID is already registered
            $stmt = $pdo->prepare("
                SELECT * FROM license_devices 
                WHERE license_key = ? AND hardware_id = ?
            ");
            $stmt->execute([$licenseKey, $hardwareId]);
            $existingDevice = $stmt->fetch();
            
            if (!$existingDevice) {
                echo json_encode(['success' => false, 'error' => 'Device limit exceeded']);
                return;
            }
        }
        
        echo json_encode([
            'success' => true,
            'license' => [
                'type' => $license['license_type'],
                'expires' => $license['expires_at'],
                'max_devices' => $license['max_devices'],
                'current_devices' => $deviceCount,
                'customer_name' => $license['customer_name']
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Validation failed']);
    }
}

function activateLicense($pdo) {
    $licenseKey = $_POST['license_key'] ?? '';
    $email = $_POST['email'] ?? '';
    $hardwareId = $_POST['hardware_id'] ?? '';
    
    if (!$licenseKey || !$email || !$hardwareId) {
        echo json_encode(['success' => false, 'error' => 'Missing required fields']);
        return;
    }
    
    try {
        // Debug logging
        error_log("License activation attempt - Key: " . substr($licenseKey, 0, 20) . "..., Email: " . $email);

        // First validate the license
        $stmt = $pdo->prepare("
            SELECT * FROM licenses
            WHERE license_key = ? AND customer_email = ? AND status = 'active'
        ");
        $stmt->execute([$licenseKey, $email]);
        $license = $stmt->fetch();

        // Debug logging
        error_log("License query result: " . ($license ? "Found" : "Not found"));
        if (!$license) {
            // Check if license exists with different status
            $stmt2 = $pdo->prepare("SELECT * FROM licenses WHERE license_key = ?");
            $stmt2->execute([$licenseKey]);
            $anyLicense = $stmt2->fetch();
            error_log("License exists with any status: " . ($anyLicense ? "Yes" : "No"));
            if ($anyLicense) {
                error_log("License status: " . $anyLicense['status'] . ", Email: " . $anyLicense['customer_email']);
            }
        }

        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'Invalid license key or email']);
            return;
        }
        
        // Check if license is expired
        $expiryDate = new DateTime($license['expires_at']);
        $currentDate = new DateTime();

        if ($expiryDate < $currentDate) {
            echo json_encode(['success' => false, 'error' => 'License has expired']);
            return;
        }
        
        // Check if device is already registered
        $stmt = $pdo->prepare("
            SELECT * FROM license_devices 
            WHERE license_key = ? AND hardware_id = ?
        ");
        $stmt->execute([$licenseKey, $hardwareId]);
        $existingDevice = $stmt->fetch();
        
        if ($existingDevice) {
            // Update last seen
            $stmt = $pdo->prepare("
                UPDATE license_devices 
                SET last_seen = NOW() 
                WHERE license_key = ? AND hardware_id = ?
            ");
            $stmt->execute([$licenseKey, $hardwareId]);
        } else {
            // Check device limit
            $stmt = $pdo->prepare("
                SELECT COUNT(*) as device_count 
                FROM license_devices 
                WHERE license_key = ?
            ");
            $stmt->execute([$licenseKey]);
            $deviceCount = $stmt->fetch()['device_count'];
            
            if ($deviceCount >= $license['max_devices']) {
                echo json_encode(['success' => false, 'error' => 'Device limit exceeded']);
                return;
            }
            
            // Register new device
            $stmt = $pdo->prepare("
                INSERT INTO license_devices (license_key, hardware_id, first_seen, last_seen) 
                VALUES (?, ?, NOW(), NOW())
            ");
            $stmt->execute([$licenseKey, $hardwareId]);
        }
        
        // Get updated device count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as device_count 
            FROM license_devices 
            WHERE license_key = ?
        ");
        $stmt->execute([$licenseKey]);
        $deviceCount = $stmt->fetch()['device_count'];
        
        echo json_encode([
            'success' => true,
            'message' => 'License activated successfully',
            'license' => [
                'type' => $license['license_type'],
                'expires' => $license['expires_at'],
                'max_devices' => $license['max_devices'],
                'current_devices' => $deviceCount,
                'customer_name' => $license['customer_name']
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Activation failed']);
    }
}

function checkLicenseStatus($pdo) {
    $hardwareId = $_GET['hardware_id'] ?? '';
    
    if (!$hardwareId) {
        echo json_encode(['success' => false, 'error' => 'Hardware ID required']);
        return;
    }
    
    try {
        // Find active license for this device
        $stmt = $pdo->prepare("
            SELECT l.*, ld.last_seen 
            FROM licenses l 
            JOIN license_devices ld ON l.license_key = ld.license_key 
            WHERE ld.hardware_id = ? AND l.status = 'active'
            ORDER BY ld.last_seen DESC 
            LIMIT 1
        ");
        $stmt->execute([$hardwareId]);
        $license = $stmt->fetch();
        
        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'No active license found']);
            return;
        }
        
        // Check if license is expired
        $expiryDate = new DateTime($license['expires_at']);
        $currentDate = new DateTime();

        if ($expiryDate < $currentDate) {
            echo json_encode(['success' => false, 'error' => 'License has expired']);
            return;
        }
        
        // Get device count
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as device_count 
            FROM license_devices 
            WHERE license_key = ?
        ");
        $stmt->execute([$license['license_key']]);
        $deviceCount = $stmt->fetch()['device_count'];
        
        // Calculate days left
        $daysLeft = $currentDate->diff($expiryDate)->days;
        
        echo json_encode([
            'success' => true,
            'license' => [
                'type' => $license['license_type'],
                'expires' => $license['expires_at'],
                'days_left' => $daysLeft,
                'max_devices' => $license['max_devices'],
                'current_devices' => $deviceCount,
                'customer_name' => $license['customer_name'],
                'license_key' => $license['license_key']
            ]
        ]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => 'Status check failed']);
    }
}
?>
