<?php
/**
 * Authentication Management Class
 * 
 * Handles user authentication, session management, password validation,
 * account lockout, and security logging for the admin system.
 */

require_once __DIR__ . '/Database.php';
require_once __DIR__ . '/TwoFactorAuth.php';
require_once __DIR__ . '/SecurityLogger.php';

class Auth {
    private $db;
    private $twoFA;
    private $logger;
    private $currentUser = null;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->twoFA = new TwoFactorAuth();
        $this->logger = new SecurityLogger();
        
        // Start session if not already started
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }
        
        // Load current user if session exists
        $this->loadCurrentUser();
    }
    
    /**
     * Authenticate user with username/email and password
     */
    public function login($username, $password, $twoFactorCode = null, $rememberMe = false) {
        $ip = $this->getClientIP();
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        try {
            // Check rate limiting
            if (!$this->checkRateLimit($ip)) {
                $this->logger->logFailedLogin($username, $ip, 'Rate limit exceeded');
                throw new Exception('Too many login attempts. Please try again later.');
            }
            
            // Find user by username or email
            $user = $this->findUser($username);
            if (!$user) {
                $this->logger->logFailedLogin($username, $ip, 'User not found');
                throw new Exception('Invalid credentials');
            }
            
            // Check if account is locked
            if ($this->isAccountLocked($user)) {
                $this->logger->logFailedLogin($username, $ip, 'Account locked');
                throw new Exception('Account is temporarily locked due to failed login attempts');
            }
            
            // Verify password
            if (!password_verify($password, $user['password_hash'])) {
                $this->handleFailedLogin($user['id'], $ip);
                $this->logger->logFailedLogin($username, $ip, 'Invalid password');
                throw new Exception('Invalid credentials');
            }
            
            // Check 2FA if enabled
            if ($user['2fa_enabled']) {
                if (!$twoFactorCode) {
                    return ['requires_2fa' => true, 'user_id' => $user['id']];
                }
                
                if (!$this->twoFA->verifyCode($user['2fa_secret'], $twoFactorCode)) {
                    $this->handleFailedLogin($user['id'], $ip);
                    $this->logger->logFailedLogin($username, $ip, 'Invalid 2FA code');
                    throw new Exception('Invalid two-factor authentication code');
                }
            }
            
            // Successful login
            $this->handleSuccessfulLogin($user, $ip, $userAgent, $rememberMe);
            $this->logger->logSuccessfulLogin($user['id'], $ip);
            
            return ['success' => true, 'user' => $this->sanitizeUserData($user)];
            
        } catch (Exception $e) {
            $this->logLoginAttempt($ip, $username, false, $e->getMessage());
            throw $e;
        }
    }
    
    /**
     * Handle superadmin emergency login
     */
    public function emergencyLogin($username, $password) {
        $superadminConfig = getConfig('superadmin');
        
        if ($username !== $superadminConfig['emergency_username']) {
            throw new Exception('Invalid emergency credentials');
        }
        
        if (!password_verify($password, $superadminConfig['emergency_password_hash'])) {
            throw new Exception('Invalid emergency credentials');
        }
        
        // Create temporary superadmin session
        $emergencyUser = [
            'id' => 0,
            'username' => $username,
            'email' => '<EMAIL>',
            'role' => 'superadmin',
            'is_emergency' => true
        ];
        
        $this->createSession($emergencyUser, $this->getClientIP(), $_SERVER['HTTP_USER_AGENT'] ?? '', false);
        $this->logger->logEmergencyLogin($this->getClientIP());
        
        return ['success' => true, 'user' => $emergencyUser];
    }
    
    /**
     * Logout user
     */
    public function logout() {
        if ($this->currentUser) {
            $this->destroySession();
            $this->logger->logLogout($this->currentUser['id'], $this->getClientIP());
        }
        
        $this->currentUser = null;
        session_destroy();
    }
    
    /**
     * Check if user is authenticated
     */
    public function isAuthenticated() {
        return $this->currentUser !== null;
    }
    
    /**
     * Get current authenticated user
     */
    public function getCurrentUser() {
        return $this->currentUser;
    }
    
    /**
     * Check if user has specific role
     */
    public function hasRole($role) {
        if (!$this->currentUser) {
            return false;
        }
        
        $userRole = $this->currentUser['role'];
        
        // Superadmin has all permissions
        if ($userRole === 'superadmin') {
            return true;
        }
        
        // Admin has moderator permissions
        if ($userRole === 'admin' && $role === 'moderator') {
            return true;
        }
        
        return $userRole === $role;
    }
    
    /**
     * Change user password
     */
    public function changePassword($userId, $currentPassword, $newPassword) {
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new Exception('User not found');
        }
        
        // Verify current password (skip for superadmin emergency reset)
        if (!$this->currentUser['is_emergency'] ?? false) {
            if (!password_verify($currentPassword, $user['password_hash'])) {
                throw new Exception('Current password is incorrect');
            }
        }
        
        // Validate new password
        $this->validatePassword($newPassword);
        
        // Update password
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);
        $this->db->update('admin_users', 
            ['password_hash' => $passwordHash, 'updated_at' => date('Y-m-d H:i:s')],
            'id = :id',
            [':id' => $userId]
        );
        
        $this->logger->logPasswordChange($userId, $this->getClientIP());
        
        return true;
    }
    
    /**
     * Reset account lockout (superadmin only)
     */
    public function resetAccountLockout($userId) {
        if (!$this->hasRole('superadmin')) {
            throw new Exception('Insufficient permissions');
        }
        
        $this->db->update('admin_users',
            ['failed_attempts' => 0, 'locked_until' => null],
            'id = :id',
            [':id' => $userId]
        );
        
        $this->logger->logAccountUnlock($userId, $this->currentUser['id'], $this->getClientIP());
        
        return true;
    }
    
    /**
     * Find user by username or email
     */
    private function findUser($identifier) {
        $sql = "SELECT * FROM admin_users WHERE (username = :identifier OR email = :identifier) AND is_active = 1";
        return $this->db->fetchRow($sql, [':identifier' => $identifier]);
    }
    
    /**
     * Check if account is locked
     */
    private function isAccountLocked($user) {
        if (!$user['locked_until']) {
            return false;
        }
        
        $lockedUntil = new DateTime($user['locked_until']);
        $now = new DateTime();
        
        if ($now < $lockedUntil) {
            return true;
        }
        
        // Unlock account if lock period has expired
        $this->db->update('admin_users',
            ['locked_until' => null, 'failed_attempts' => 0],
            'id = :id',
            [':id' => $user['id']]
        );
        
        return false;
    }
    
    /**
     * Handle failed login attempt
     */
    private function handleFailedLogin($userId, $ip) {
        $securityConfig = getConfig('security');
        
        // Increment failed attempts
        $this->db->query(
            "UPDATE admin_users SET failed_attempts = failed_attempts + 1 WHERE id = :id",
            [':id' => $userId]
        );
        
        // Get updated user data
        $user = $this->getUserById($userId);
        
        // Lock account if max attempts reached
        if ($user['failed_attempts'] >= $securityConfig['max_failed_attempts']) {
            $lockDuration = $securityConfig['lockout_duration'];
            
            // Progressive lockout - increase duration for repeated lockouts
            if ($securityConfig['lockout_progressive']) {
                $lockDuration *= min($user['failed_attempts'] - $securityConfig['max_failed_attempts'] + 1, 5);
            }
            
            $lockedUntil = date('Y-m-d H:i:s', time() + $lockDuration);
            
            $this->db->update('admin_users',
                ['locked_until' => $lockedUntil],
                'id = :id',
                [':id' => $userId]
            );
            
            $this->logger->logAccountLock($userId, $ip, $user['failed_attempts']);
        }
    }
    
    /**
     * Handle successful login
     */
    private function handleSuccessfulLogin($user, $ip, $userAgent, $rememberMe) {
        // Reset failed attempts
        $this->db->update('admin_users',
            ['failed_attempts' => 0, 'locked_until' => null, 'last_login' => date('Y-m-d H:i:s')],
            'id = :id',
            [':id' => $user['id']]
        );
        
        // Create session
        $this->createSession($user, $ip, $userAgent, $rememberMe);
        
        // Log successful attempt
        $this->logLoginAttempt($ip, $user['username'], true);
    }
    
    /**
     * Create user session
     */
    private function createSession($user, $ip, $userAgent, $rememberMe) {
        $sessionId = bin2hex(random_bytes(32));
        $securityConfig = getConfig('security');
        
        $expiresAt = $rememberMe 
            ? date('Y-m-d H:i:s', time() + $securityConfig['remember_me_timeout'])
            : date('Y-m-d H:i:s', time() + $securityConfig['session_timeout']);
        
        // Store session in database
        if ($user['id'] > 0) { // Skip for emergency sessions
            $this->db->insert('login_sessions', [
                'session_id' => $sessionId,
                'admin_id' => $user['id'],
                'ip_address' => $ip,
                'user_agent' => $userAgent,
                'expires_at' => $expiresAt,
                'remember_me' => $rememberMe ? 1 : 0
            ]);
        }
        
        // Set session variables
        $_SESSION['admin_session_id'] = $sessionId;
        $_SESSION['admin_user_id'] = $user['id'];
        $_SESSION['admin_username'] = $user['username'];
        $_SESSION['admin_role'] = $user['role'];
        $_SESSION['admin_last_activity'] = time();
        $_SESSION['admin_ip'] = $ip;
        
        if (isset($user['is_emergency'])) {
            $_SESSION['admin_emergency'] = true;
        }
        
        $this->currentUser = $this->sanitizeUserData($user);
        
        // Regenerate session ID for security
        session_regenerate_id(true);
    }
    
    /**
     * Load current user from session
     */
    private function loadCurrentUser() {
        if (!isset($_SESSION['admin_session_id']) || !isset($_SESSION['admin_user_id'])) {
            return;
        }
        
        $sessionId = $_SESSION['admin_session_id'];
        $userId = $_SESSION['admin_user_id'];
        $currentIP = $this->getClientIP();
        
        // Handle emergency sessions
        if (isset($_SESSION['admin_emergency'])) {
            $this->currentUser = [
                'id' => 0,
                'username' => $_SESSION['admin_username'],
                'email' => '<EMAIL>',
                'role' => 'superadmin',
                'is_emergency' => true
            ];
            return;
        }
        
        // Validate session
        $session = $this->db->fetchRow(
            "SELECT s.*, u.username, u.email, u.role, u.is_active 
             FROM login_sessions s 
             JOIN admin_users u ON s.admin_id = u.id 
             WHERE s.session_id = :session_id AND s.is_active = 1 AND s.expires_at > NOW()",
            [':session_id' => $sessionId]
        );
        
        if (!$session || $session['admin_id'] != $userId) {
            $this->destroySession();
            return;
        }
        
        // Check IP consistency (optional security measure)
        if ($session['ip_address'] !== $currentIP) {
            $this->logger->logSuspiciousActivity($userId, $currentIP, 'IP address mismatch');
            // Optionally destroy session or just log
        }
        
        // Check session timeout
        $lastActivity = $_SESSION['admin_last_activity'] ?? 0;
        $securityConfig = getConfig('security');
        
        if (time() - $lastActivity > $securityConfig['session_timeout']) {
            $this->destroySession();
            return;
        }
        
        // Update last activity
        $_SESSION['admin_last_activity'] = time();
        
        // Update session in database
        $this->db->update('login_sessions',
            ['last_activity' => date('Y-m-d H:i:s')],
            'session_id = :session_id',
            [':session_id' => $sessionId]
        );
        
        $this->currentUser = $this->sanitizeUserData($session);
    }
    
    /**
     * Destroy current session
     */
    private function destroySession() {
        if (isset($_SESSION['admin_session_id'])) {
            $this->db->update('login_sessions',
                ['is_active' => 0],
                'session_id = :session_id',
                [':session_id' => $_SESSION['admin_session_id']]
            );
        }
        
        // Clear session variables
        unset($_SESSION['admin_session_id']);
        unset($_SESSION['admin_user_id']);
        unset($_SESSION['admin_username']);
        unset($_SESSION['admin_role']);
        unset($_SESSION['admin_last_activity']);
        unset($_SESSION['admin_ip']);
        unset($_SESSION['admin_emergency']);
    }
    
    /**
     * Check rate limiting
     */
    private function checkRateLimit($ip) {
        $securityConfig = getConfig('security');
        $window = $securityConfig['rate_limit_window'];
        $maxAttempts = $securityConfig['rate_limit_max_attempts'];
        
        $count = $this->db->fetchColumn(
            "SELECT COUNT(*) FROM login_attempts 
             WHERE ip_address = :ip AND created_at > DATE_SUB(NOW(), INTERVAL :window SECOND)",
            [':ip' => $ip, ':window' => $window]
        );
        
        return $count < $maxAttempts;
    }
    
    /**
     * Log login attempt
     */
    private function logLoginAttempt($ip, $username, $success, $failureReason = null) {
        $this->db->insert('login_attempts', [
            'ip_address' => $ip,
            'username' => $username,
            'success' => $success ? 1 : 0,
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? '',
            'failure_reason' => $failureReason
        ]);
    }
    
    /**
     * Get user by ID
     */
    private function getUserById($id) {
        return $this->db->fetchRow(
            "SELECT * FROM admin_users WHERE id = :id",
            [':id' => $id]
        );
    }
    
    /**
     * Validate password strength
     */
    private function validatePassword($password) {
        $config = getConfig('security');
        
        if (strlen($password) < $config['password_min_length']) {
            throw new Exception("Password must be at least {$config['password_min_length']} characters long");
        }
        
        if ($config['password_require_uppercase'] && !preg_match('/[A-Z]/', $password)) {
            throw new Exception("Password must contain at least one uppercase letter");
        }
        
        if ($config['password_require_lowercase'] && !preg_match('/[a-z]/', $password)) {
            throw new Exception("Password must contain at least one lowercase letter");
        }
        
        if ($config['password_require_numbers'] && !preg_match('/[0-9]/', $password)) {
            throw new Exception("Password must contain at least one number");
        }
        
        if ($config['password_require_special']) {
            $specialChars = preg_quote($config['password_special_chars'], '/');
            if (!preg_match("/[$specialChars]/", $password)) {
                throw new Exception("Password must contain at least one special character");
            }
        }
        
        return true;
    }
    
    /**
     * Sanitize user data for output
     */
    private function sanitizeUserData($user) {
        return [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'role' => $user['role'],
            'last_login' => $user['last_login'] ?? null,
            '2fa_enabled' => $user['2fa_enabled'] ?? false,
            'is_emergency' => $user['is_emergency'] ?? false
        ];
    }
    
    /**
     * Request password reset
     */
    public function requestPasswordReset($email) {
        $user = $this->db->fetchRow(
            "SELECT * FROM admin_users WHERE email = :email AND is_active = 1",
            [':email' => $email]
        );

        if (!$user) {
            // Don't reveal if email exists or not
            return true;
        }

        // Check if there's already a recent reset request
        $recentRequest = $this->db->fetchRow(
            "SELECT * FROM password_reset_tokens
             WHERE email = :email AND used = 0 AND expires_at > NOW()
             ORDER BY created_at DESC LIMIT 1",
            [':email' => $email]
        );

        if ($recentRequest) {
            // Don't allow multiple requests within 5 minutes
            $timeDiff = time() - strtotime($recentRequest['created_at']);
            if ($timeDiff < 300) {
                throw new Exception('A password reset email was already sent recently. Please check your email or wait 5 minutes before requesting again.');
            }
        }

        // Generate reset token
        $token = bin2hex(random_bytes(32));
        $expiresAt = date('Y-m-d H:i:s', time() + getConfig('security', 'reset_token_expiry', 3600));

        // Store reset token
        $this->db->insert('password_reset_tokens', [
            'token' => $token,
            'email' => $email,
            'admin_id' => $user['id'],
            'expires_at' => $expiresAt,
            'ip_address' => $this->getClientIP()
        ]);

        // Log the request
        $this->logger->logPasswordResetRequest($email, $this->getClientIP());

        // Send reset email (in production, implement actual email sending)
        $this->sendPasswordResetEmail($email, $token, $user['username']);

        return true;
    }

    /**
     * Reset password with token
     */
    public function resetPassword($token, $newPassword) {
        // Find valid token
        $resetToken = $this->db->fetchRow(
            "SELECT * FROM password_reset_tokens
             WHERE token = :token AND used = 0 AND expires_at > NOW()",
            [':token' => $token]
        );

        if (!$resetToken) {
            throw new Exception('Invalid or expired reset token.');
        }

        // Get user
        $user = $this->getUserById($resetToken['admin_id']);
        if (!$user) {
            throw new Exception('User not found.');
        }

        // Validate new password
        $this->validatePassword($newPassword);

        // Update password
        $passwordHash = password_hash($newPassword, PASSWORD_DEFAULT);

        $this->db->beginTransaction();
        try {
            // Update password
            $this->db->update('admin_users',
                ['password_hash' => $passwordHash, 'updated_at' => date('Y-m-d H:i:s')],
                'id = :id',
                [':id' => $user['id']]
            );

            // Mark token as used
            $this->db->update('password_reset_tokens',
                ['used' => 1, 'used_at' => date('Y-m-d H:i:s')],
                'token = :token',
                [':token' => $token]
            );

            // Invalidate all existing sessions for this user
            $this->db->update('login_sessions',
                ['is_active' => 0],
                'admin_id = :admin_id',
                [':admin_id' => $user['id']]
            );

            $this->db->commit();

            // Log the password reset
            $this->logger->logPasswordResetComplete($user['id'], $this->getClientIP());

            return true;

        } catch (Exception $e) {
            $this->db->rollback();
            throw $e;
        }
    }

    /**
     * Send password reset email
     */
    private function sendPasswordResetEmail($email, $token, $username) {
        $appConfig = getConfig('app');
        $resetUrl = $appConfig['app_url'] . "/reset-password.php?token=" . $token;

        // In development, just log the reset URL
        if (ENVIRONMENT === 'development') {
            error_log("Password reset URL for $email: $resetUrl");
            return true;
        }

        // In production, implement actual email sending
        // This is a placeholder for email functionality
        $subject = "Password Reset - " . $appConfig['app_name'];
        $message = "
            Hello $username,

            You have requested a password reset for your admin account.

            Click the following link to reset your password:
            $resetUrl

            This link will expire in 1 hour.

            If you did not request this reset, please ignore this email.

            Best regards,
            " . $appConfig['app_name'] . " Team
        ";

        // Use mail() function or a proper email library like PHPMailer
        return mail($email, $subject, $message);
    }

    /**
     * Get client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];

        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                // Handle comma-separated IPs (from proxies)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }

        return $_SERVER['REMOTE_ADDR'] ?? '127.0.0.1';
    }
}
?>
