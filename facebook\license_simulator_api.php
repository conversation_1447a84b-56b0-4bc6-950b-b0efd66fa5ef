<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// Load configuration
require_once __DIR__ . '/config.php';

try {
    $pdo = getFacebookDatabase();
} catch (Exception $e) {
    logFacebookError('Database connection failed', ['error' => $e->getMessage()]);
    echo json_encode(['success' => false, 'error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Get JSON input
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

switch ($action) {
    case 'expire_license':
        expireLicense($pdo, $input);
        break;
    
    case 'reset_expiry':
        resetLicenseExpiry($pdo, $input);
        break;
    
    case 'clear_devices':
        clearAllDevices($pdo, $input);
        break;
    
    case 'get_devices':
        getDevices($pdo, $input);
        break;
    
    default:
        echo json_encode(['success' => false, 'error' => 'Invalid action']);
        break;
}

function expireLicense($pdo, $input) {
    try {
        // Find the license associated with this hardware ID
        $stmt = $pdo->prepare("
            SELECT l.license_key 
            FROM licenses l 
            JOIN license_devices ld ON l.license_key = ld.license_key 
            WHERE ld.hardware_id = ?
        ");
        $stmt->execute([$input['hardware_id']]);
        $license = $stmt->fetch();
        
        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'No license found for this device']);
            return;
        }
        
        // Set license to expire yesterday
        $yesterday = date('Y-m-d H:i:s', strtotime('-1 day'));
        $stmt = $pdo->prepare("UPDATE licenses SET expires_at = ? WHERE license_key = ?");
        $stmt->execute([$yesterday, $license['license_key']]);
        
        echo json_encode(['success' => true, 'message' => 'License expired successfully']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function resetLicenseExpiry($pdo, $input) {
    try {
        // Find the license associated with this hardware ID
        $stmt = $pdo->prepare("
            SELECT l.license_key 
            FROM licenses l 
            JOIN license_devices ld ON l.license_key = ld.license_key 
            WHERE ld.hardware_id = ?
        ");
        $stmt->execute([$input['hardware_id']]);
        $license = $stmt->fetch();
        
        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'No license found for this device']);
            return;
        }
        
        // Set license to expire in 1 year
        $nextYear = date('Y-m-d H:i:s', strtotime('+1 year'));
        $stmt = $pdo->prepare("UPDATE licenses SET expires_at = ? WHERE license_key = ?");
        $stmt->execute([$nextYear, $license['license_key']]);
        
        echo json_encode(['success' => true, 'message' => 'License expiry reset successfully']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function clearAllDevices($pdo, $input) {
    try {
        // Find the license associated with this hardware ID
        $stmt = $pdo->prepare("
            SELECT l.license_key 
            FROM licenses l 
            JOIN license_devices ld ON l.license_key = ld.license_key 
            WHERE ld.hardware_id = ?
        ");
        $stmt->execute([$input['hardware_id']]);
        $license = $stmt->fetch();
        
        if (!$license) {
            echo json_encode(['success' => false, 'error' => 'No license found for this device']);
            return;
        }
        
        // Clear all devices for this license
        $stmt = $pdo->prepare("DELETE FROM license_devices WHERE license_key = ?");
        $stmt->execute([$license['license_key']]);
        
        echo json_encode(['success' => true, 'message' => 'All devices cleared successfully']);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}

function getDevices($pdo, $input) {
    try {
        // Find the license associated with this hardware ID
        $stmt = $pdo->prepare("
            SELECT l.license_key 
            FROM licenses l 
            JOIN license_devices ld ON l.license_key = ld.license_key 
            WHERE ld.hardware_id = ?
        ");
        $stmt->execute([$input['hardware_id']]);
        $license = $stmt->fetch();
        
        if (!$license) {
            // Try to find any license for testing
            $stmt = $pdo->prepare("SELECT license_key FROM licenses LIMIT 1");
            $stmt->execute();
            $license = $stmt->fetch();
            
            if (!$license) {
                echo json_encode(['success' => true, 'devices' => []]);
                return;
            }
        }
        
        // Get all devices for this license
        $stmt = $pdo->prepare("
            SELECT hardware_id, last_seen, created_at 
            FROM license_devices 
            WHERE license_key = ? 
            ORDER BY created_at DESC
        ");
        $stmt->execute([$license['license_key']]);
        $devices = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode(['success' => true, 'devices' => $devices]);
        
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
    }
}
?>
