<?php
/**
 * Working Graph Search Application
 * 
 * Integrated with database authentication
 */

// Load configuration first (before session_start)
define('ADMIN_SYSTEM_LOADED', true);
require_once __DIR__ . '/config/config.php';

session_start();

// Check authentication
if (!isset($_SESSION['admin_logged_in']) || !$_SESSION['admin_logged_in']) {
    header('Location: login.php');
    exit;
}

$username = $_SESSION['admin_username'] ?? 'Unknown';
$role = $_SESSION['admin_role'] ?? 'user';

// Include LicenseManager for database operations
require_once __DIR__ . '/classes/LicenseManager.php';

// Handle AJAX requests for license operations
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    header('Content-Type: application/json');

    try {
        $licenseManager = new LicenseManager();

        switch ($_POST['action']) {
            case 'generate_license':
                $licenseData = [
                    'customer_name' => trim($_POST['customer_name'] ?? ''),
                    'customer_email' => trim($_POST['customer_email'] ?? ''),
                    'license_type' => $_POST['license_type'] ?? 'basic',
                    'duration_months' => intval($_POST['duration_months'] ?? 12),
                    'max_devices' => intval($_POST['max_devices'] ?? 3),
                    'notes' => trim($_POST['notes'] ?? ''),
                    'created_by' => $_SESSION['admin_user_id'] ?? null,
                    'is_upgrade' => isset($_POST['is_upgrade']) && $_POST['is_upgrade'] === 'true'
                ];

                $result = $licenseManager->generateLicense($licenseData);
                echo json_encode(['success' => true, 'license' => $result]);
                exit;

            case 'get_licenses':
                $licenses = $licenseManager->getLicenses();

                // Add device count to each license
                foreach ($licenses as &$license) {
                    $license['devices_used'] = $licenseManager->getDeviceCount($license['id']);
                }

                echo json_encode(['success' => true, 'licenses' => $licenses]);
                exit;

            case 'get_stats':
                $stats = $licenseManager->getLicenseStats();
                echo json_encode(['success' => true, 'stats' => $stats]);
                exit;

            case 'update_license':
                $licenseId = $_POST['license_id'] ?? null;
                $status = $_POST['status'] ?? null;

                if (!$licenseId || !$status) {
                    throw new Exception('License ID and status are required');
                }

                $updateData = ['status' => $status];
                $updatedLicense = $licenseManager->updateLicense($licenseId, $updateData);

                echo json_encode(['success' => true, 'license' => $updatedLicense]);
                exit;

            case 'delete_license':
                $licenseId = $_POST['license_id'] ?? null;

                if (!$licenseId) {
                    throw new Exception('License ID is required');
                }

                $deleted = $licenseManager->deleteLicense($licenseId);

                if ($deleted) {
                    echo json_encode(['success' => true, 'message' => 'License deleted successfully']);
                } else {
                    throw new Exception('Failed to delete license');
                }
                exit;

            case 'revoke_license':
                $licenseId = $_POST['license_id'] ?? null;
                $reason = trim($_POST['reason'] ?? '');

                if (!$licenseId) {
                    throw new Exception('License ID is required');
                }

                $revoked = $licenseManager->revokeLicense($licenseId, $reason);

                if ($revoked) {
                    echo json_encode(['success' => true, 'message' => 'License revoked successfully']);
                } else {
                    throw new Exception('Failed to revoke license');
                }
                exit;

            case 'debug_license_key':
                $licenseId = $_POST['license_id'] ?? null;
                if (!$licenseId) {
                    throw new Exception('License ID is required');
                }

                $license = $licenseManager->getLicenseById($licenseId);
                if (!$license) {
                    throw new Exception('License not found');
                }

                echo json_encode([
                    'success' => true,
                    'license_key' => $license['license_key'],
                    'key_length' => strlen($license['license_key']),
                    'customer_email' => $license['customer_email']
                ]);
                exit;

            default:
                throw new Exception('Invalid action');
        }
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'error' => $e->getMessage()]);
        exit;
    }
}
?>
<!DOCTYPE html>
<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Graph Search - Admin Interface</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        linkedin: {
                            blue: '#0077b5',
                            darkBlue: '#004471'
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .sidebar {
            transition: transform 0.3s ease;
        }
        .sidebar.hidden {
            transform: translateX(-100%);
        }
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                z-index: 50;
                height: 100vh;
            }
        }

        .license-key {
            font-family: 'Courier New', monospace;
            background: #f3f4f6;
            color: #1f2937;
            padding: 8px 12px;
            border-radius: 6px;
            border: 1px solid #d1d5db;
            font-size: 14px;
            letter-spacing: 1px;
        }
        .dark .license-key {
            background: #374151;
            color: #f9fafb;
            border-color: #4b5563;
        }
        .status-active { color: #10b981; }
        .status-expired { color: #ef4444; }
        .status-suspended { color: #f59e0b; }
        .tier-trial { background: #fef3c7; color: #d97706; }
        .tier-basic { background: #dbeafe; color: #2563eb; }
        .tier-professional { background: #dcfce7; color: #16a34a; }
        .tier-enterprise { background: #f3e8ff; color: #9333ea; }
        .dark .tier-trial { background: #d97706; color: #fef3c7; }
        .dark .tier-basic { background: #2563eb; color: #dbeafe; }
        .dark .tier-professional { background: #16a34a; color: #dcfce7; }
        .dark .tier-enterprise { background: #9333ea; color: #f3e8ff; }

        /* License table styles - matching form pattern */
        .license-table {
            font-size: 0.875rem;
        }
        .license-table td {
            padding: 0.75rem 1rem;
        }
        .license-table th {
            padding: 0.75rem 1rem;
        }

        .action-btn {
            transition: all 0.2s ease;
            border-radius: 4px;
            padding: 6px;
            min-width: 28px;
            min-height: 28px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <button id="sidebarToggle" class="md:hidden mr-4 text-gray-600 hover:text-gray-900">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-xl font-bold text-gray-800">
                        <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                        License Management System
                    </h1>
                </div>

                <div class="flex items-center space-x-4">
                    <!-- User Info -->
                    <div class="flex items-center space-x-2">
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-700"><?php echo htmlspecialchars($username); ?></p>
                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($role); ?></p>
                        </div>
                        <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm"><?php echo strtoupper(substr($username, 0, 1)); ?></span>
                        </div>
                    </div>

                    <!-- Logout -->
                    <a href="login.php" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex">
        <!-- Sidebar -->
        <aside id="sidebar" class="sidebar bg-white w-64 shadow-lg">
            <div class="p-6">
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-tachometer-alt mr-3"></i>Dashboard
                    </a>
                    <a href="graphsearch.php" class="flex items-center px-4 py-2 text-blue-600 bg-blue-50 rounded-lg">
                        <i class="fas fa-key mr-3"></i>License Management
                        <span class="ml-auto bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">ACTIVE</span>
                    </a>
                    <a href="../graphsearch.html" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-search mr-3"></i>Launch Search
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-chart-bar mr-3"></i>Analytics
                    </a>
                    <a href="#" class="flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 rounded-lg">
                        <i class="fas fa-cog mr-3"></i>Settings
                    </a>
                </nav>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="flex-1 p-6">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-2xl font-bold text-gray-800 mb-2">
                License Management System 🔑
            </h2>
            <p class="text-gray-600">
                Generate, manage, and monitor your Graph Search licenses.
            </p>
        </div>

        <!-- Dashboard Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                        <i class="fas fa-key"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Total Licenses</p>
                        <p id="totalLicenses" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 text-green-600">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Active Licenses</p>
                        <p id="activeLicenses" class="text-2xl font-bold text-gray-900">0</p>
                    </div>
                </div>
            </div>
            <div class="bg-white rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-purple-100 text-purple-600">
                        <i class="fas fa-dollar-sign"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-600">Revenue (Monthly)</p>
                        <p id="monthlyRevenue" class="text-2xl font-bold text-gray-900">$0</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Generate New License Section -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                <h2 class="text-xl font-semibold mb-6 flex items-center">
                    <i class="fas fa-plus-circle mr-2 text-green-500"></i>Generate New License
                </h2>

                <form id="licenseForm" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label class="block text-sm font-medium mb-2">License Type</label>
                        <select id="licenseType" class="w-full p-3 border rounded-lg">
                            <option value="basic">Basic ($29/mo)</option>
                            <option value="professional">Professional ($79/mo)</option>
                            <option value="enterprise">Enterprise ($199/mo)</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">Customer Email</label>
                        <input type="email" id="customerEmail" placeholder="<EMAIL>" class="w-full p-3 border rounded-lg">
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">Customer Name</label>
                        <input type="text" id="customerName" placeholder="John Doe" class="w-full p-3 border rounded-lg">
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">Duration</label>
                        <select id="duration" class="w-full p-3 border rounded-lg">
                            <option value="1">1 Month</option>
                            <option value="3">3 Months</option>
                            <option value="6">6 Months</option>
                            <option value="12" selected>12 Months</option>
                            <option value="24">24 Months</option>
                            <option value="36">36 Months</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">Max Devices</label>
                        <select id="maxDevices" class="w-full p-3 border rounded-lg">
                            <option value="1">1 Device</option>
                            <option value="2">2 Devices</option>
                            <option value="3" selected>3 Devices</option>
                            <option value="5">5 Devices</option>
                            <option value="10">10 Devices</option>
                            <option value="25">25 Devices</option>
                            <option value="50">50 Devices</option>
                            <option value="-1">Unlimited Devices</option>
                        </select>
                    </div>

                    <div>
                        <label class="block text-sm font-medium mb-2">Notes (Optional)</label>
                        <textarea id="licenseNotes" rows="2" placeholder="Internal notes..." class="w-full p-3 border rounded-lg"></textarea>
                    </div>

                    <div class="md:col-span-2 lg:col-span-3">
                        <div class="flex items-center mb-4">
                            <input type="checkbox" id="isUpgrade" class="mr-2">
                            <label for="isUpgrade" class="text-sm font-medium">
                                <i class="fas fa-arrow-up mr-1 text-green-500"></i>
                                License Upgrade (Allow generating new license for existing email)
                            </label>
                        </div>
                        <div id="upgradeWarning" class="hidden mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                            <div class="flex items-center text-yellow-800">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                <span class="text-sm">
                                    <strong>Upgrade Mode:</strong> This will supersede any existing active license for this email address.
                                    Ensure the new license type is a higher tier than the existing one.
                                </span>
                            </div>
                        </div>
                        <button type="submit" id="generateBtn" class="w-full bg-green-500 text-white py-3 px-6 rounded-lg hover:bg-green-600 transition-colors font-medium">
                            <i class="fas fa-key mr-2"></i>Generate License Key
                        </button>
                    </div>
                </form>

                <!-- Generated License Display -->
                <div id="generatedLicense" class="hidden mt-6 p-4 bg-green-50 dark:bg-green-900 rounded-lg border border-green-200 dark:border-green-700">
                    <h3 class="font-semibold text-green-800 dark:text-green-200 mb-2">License Generated Successfully!</h3>
                    <div class="space-y-2">
                        <div>
                            <label class="text-sm font-medium text-green-700 dark:text-green-300">License Key:</label>
                            <div class="license-key mt-1" id="newLicenseKey" title="">XXXX-XXXX-XXXX-XXXX</div>
                            <div class="text-xs text-green-600 dark:text-green-400 mt-1">Click to view full key</div>
                        </div>
                        <div class="flex gap-2 mt-3">
                            <button id="copyLicenseKey" class="flex-1 bg-green-500 text-white py-2 px-3 rounded text-sm hover:bg-green-600">
                                <i class="fas fa-copy mr-1"></i>Copy Key
                            </button>
                            <button id="emailLicenseKey" class="flex-1 bg-blue-500 text-white py-2 px-3 rounded text-sm hover:bg-blue-600">
                                <i class="fas fa-envelope mr-1"></i>Email Customer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>



        <!-- License Management Section -->
        <div class="mb-8">
            <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-xl font-semibold flex items-center">
                            <i class="fas fa-list mr-2 text-blue-500"></i>License Management
                        </h2>
                        <div class="flex gap-2">
                            <button id="exportLicenses" class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                                <i class="fas fa-download mr-2"></i>Export
                            </button>
                            <button id="refreshLicenses" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors">
                                <i class="fas fa-sync-alt mr-2"></i>Refresh
                            </button>
                        </div>
                    </div>

                    <!-- Search and Filter -->
                    <div class="flex flex-wrap gap-3 mb-4">
                        <div class="flex-1 min-w-64">
                            <input type="text" id="searchLicenses" placeholder="Search by email, name, or license key..." class="w-full p-2 text-sm border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                        </div>
                        <select id="filterStatus" class="p-2 text-sm border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="expired">Expired</option>
                            <option value="suspended">Suspended</option>
                        </select>
                        <select id="filterType" class="p-2 text-sm border rounded-lg dark:bg-gray-700 dark:border-gray-600">
                            <option value="">All Types</option>
                            <option value="basic">Basic</option>
                            <option value="professional">Professional</option>
                            <option value="enterprise">Enterprise</option>
                        </select>
                    </div>

                    <!-- License Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full license-table">
                            <thead>
                                <tr class="border-b border-gray-200 dark:border-gray-700">
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Customer</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">License Key</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Type</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Status</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Expires</th>
                                    <th class="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="licensesTableBody">
                                <tr>
                                    <td colspan="6" class="text-center py-8 text-gray-500 dark:text-gray-400">
                                        <i class="fas fa-key text-4xl mb-2"></i>
                                        <div>No licenses generated yet</div>
                                        <div class="text-sm">Generate your first license above</div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>


        </main>
    </div>

    <script>
        // Function to shorten license key for display
        function shortenLicenseKey(licenseKey, maxLength = 25) {
            if (!licenseKey || licenseKey.length <= maxLength) {
                return licenseKey;
            }

            // For license keys like "3E6E-3D2D-3524-726A-7137-2827-4F5A-8B9C"
            // Show first part and add "..."
            const parts = licenseKey.split('-');
            if (parts.length > 3) {
                return parts.slice(0, 3).join('-') + '-...';
            } else {
                // Fallback for other formats
                return licenseKey.substring(0, maxLength - 3) + '...';
            }
        }

        $(document).ready(function() {
            // Theme toggle
            $('#toggleTheme').click(function() {
                $('html').toggleClass('dark');
                localStorage.setItem('adminTheme', $('html').hasClass('dark') ? 'dark' : 'light');
            });

            // Load saved theme
            if (localStorage.getItem('adminTheme') === 'dark') {
                $('html').addClass('dark');
            }

            // Initialize data from database
            loadLicenses();
            loadStats();

            // License generation functionality
            $('#licenseForm').on('submit', function(e) {
                e.preventDefault();
                generateLicense();
            });

            function generateLicense() {
                const type = $('#licenseType').val();
                const email = $('#customerEmail').val().trim();
                const name = $('#customerName').val().trim();
                const duration = $('#duration').val();
                const maxDevices = $('#maxDevices').val();
                const isUpgrade = $('#isUpgrade').is(':checked');

                if (!email || !name) {
                    alert('Please fill in customer email and name');
                    return;
                }

                // Show loading state
                const submitBtn = $('#licenseForm button[type="submit"]');
                const originalText = submitBtn.html();
                submitBtn.html('<i class="fas fa-spinner fa-spin mr-2"></i>Generating...').prop('disabled', true);

                // Send AJAX request to generate license
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: {
                        action: 'generate_license',
                        customer_name: name,
                        customer_email: email,
                        license_type: type,
                        duration_months: parseInt(duration),
                        max_devices: parseInt(maxDevices),
                        notes: $('#licenseNotes').val(),
                        is_upgrade: isUpgrade
                    },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            // Show generated license with shortened display
                            const fullKey = response.license.license_key;
                            const shortKey = shortenLicenseKey(fullKey);

                            $('#newLicenseKey')
                                .text(shortKey)
                                .attr('title', fullKey)
                                .attr('data-full-key', fullKey)
                                .css('cursor', 'pointer');

                            $('#generatedLicense').removeClass('hidden');

                            // Reset form
                            $('#licenseForm')[0].reset();
                            $('#licenseType').val('basic');
                            $('#duration').val('12');
                            $('#maxDevices').val('3');

                            // Refresh license table and stats
                            loadLicenses();
                            loadStats();

                            showSuccessNotification('License generated successfully! License key: ' + shortenLicenseKey(response.license.license_key));
                        } else {
                            // Show error message in a styled notification
                            showErrorNotification(response.error);
                        }
                    },
                    error: function() {
                        alert('Failed to generate license. Please try again.');
                    },
                    complete: function() {
                        submitBtn.html(originalText).prop('disabled', false);
                    }
                });
            }

            // Load licenses from database
            function loadLicenses() {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { action: 'get_licenses' },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            renderLicensesTable(response.licenses);
                        }
                    },
                    error: function() {
                        console.error('Failed to load licenses');
                    }
                });
            }

            // Load stats from database
            function loadStats() {
                $.ajax({
                    url: '',
                    method: 'POST',
                    data: { action: 'get_stats' },
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            const stats = response.stats;
                            $('#totalLicenses').text(stats.total || 0);
                            $('#activeLicenses').text(stats.active || 0);
                            $('#monthlyRevenue').text('$' + (stats.estimated_revenue || 0).toLocaleString());
                        }
                    },
                    error: function() {
                        console.error('Failed to load stats');
                    }
                });
            }

            function addLicenseToTable(license) {
                if ($('#licensesTableBody tr').first().find('td').attr('colspan')) {
                    $('#licensesTableBody').empty();
                }

                const row = `
                    <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                        <td class="py-3 px-4">
                            <div class="font-medium text-sm">${license.name}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">${license.email}</div>
                        </td>
                        <td class="py-3 px-4">
                            <div class="font-mono text-xs">${license.key}</div>
                        </td>
                        <td class="py-3 px-4">
                            <span class="px-2 py-1 rounded-full text-xs font-medium tier-${license.type}">
                                ${license.type.charAt(0).toUpperCase() + license.type.slice(1)}
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <span class="status-${license.status} text-sm">
                                <i class="fas fa-circle text-xs mr-1"></i>
                                ${license.status.charAt(0).toUpperCase() + license.status.slice(1)}
                            </span>
                        </td>
                        <td class="py-3 px-4">
                            <div class="text-sm">${license.expires}</div>
                        </td>
                        <td class="py-3 px-4">
                            <button onclick="copyToClipboard('${license.key}')" class="text-green-500 hover:text-green-700 p-1 text-sm" title="Copy Key">
                                <i class="fas fa-copy"></i>
                            </button>
                        </td>
                    </tr>
                `;
                $('#licensesTableBody').append(row);
            }

            // Replace addLicenseToTable with renderLicensesTable for database integration
            function renderLicensesTable(licenses) {
                const tbody = $('#licensesTableBody');
                tbody.empty();

                if (!licenses || licenses.length === 0) {
                    tbody.html(`
                        <tr>
                            <td colspan="6" class="py-8 text-center text-gray-500 dark:text-gray-400">
                                <i class="fas fa-key text-4xl mb-4 opacity-50"></i>
                                <p>No licenses generated yet</p>
                                <p class="text-sm">Generate your first license using the form above</p>
                            </td>
                        </tr>
                    `);
                    return;
                }

                licenses.forEach(license => {
                    const expiryDate = new Date(license.expires_at);
                    const isExpired = expiryDate < new Date();
                    const statusClass = license.status === 'active' && !isExpired ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    const displayStatus = isExpired ? 'expired' : license.status;

                    // Calculate days left
                    const today = new Date();
                    const timeDiff = expiryDate.getTime() - today.getTime();
                    const daysLeft = Math.ceil(timeDiff / (1000 * 3600 * 24));
                    const daysText = daysLeft > 0 ? `${daysLeft} days left` : `${Math.abs(daysLeft)} days ago`;

                    // Get actual device count from tracking system
                    const devicesUsed = license.devices_used || 0;
                    const maxDevices = license.max_devices || 3;

                    const row = `
                        <tr class="border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <td class="py-3 px-2">
                                <div class="font-medium">${license.customer_name}</div>
                                <div class="text-sm text-gray-500 dark:text-gray-400">${license.customer_email}</div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="license-key text-xs font-mono cursor-pointer"
                                     title="Click to view full key: ${license.license_key}"
                                     data-full-key="${license.license_key}"
                                     onclick="toggleLicenseKeyDisplay(this)">
                                    ${shortenLicenseKey(license.license_key)}
                                </div>
                            </td>
                            <td class="py-3 px-2">
                                <span class="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800">${license.license_type}</span>
                            </td>
                            <td class="py-3 px-2">
                                <span class="px-2 py-1 text-xs rounded-full ${statusClass}">${displayStatus}</span>
                            </td>
                            <td class="py-3 px-2">
                                <div class="text-sm">${expiryDate.toLocaleDateString()}</div>
                                <div class="text-xs text-gray-500 dark:text-gray-400">${daysText}</div>
                                <div class="text-xs text-blue-600">Devices: ${devicesUsed}/${maxDevices}</div>
                            </td>
                            <td class="py-3 px-2">
                                <div class="flex gap-1">
                                    <button onclick="copyLicenseKey('${license.license_key}')" class="action-btn text-green-500 hover:text-green-700" title="Copy Key">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                    ${license.status === 'active' ?
                                        `<button onclick="suspendLicense(${license.id})" class="action-btn text-yellow-500 hover:text-yellow-700" title="Suspend License">
                                            <i class="fas fa-pause"></i>
                                        </button>` :
                                        license.status === 'suspended' ?
                                        `<button onclick="activateLicense(${license.id})" class="action-btn text-blue-500 hover:text-blue-700" title="Activate License">
                                            <i class="fas fa-play"></i>
                                        </button>` : ''
                                    }
                                    ${license.status !== 'revoked' ?
                                        `<button onclick="revokeLicense(${license.id})" class="action-btn text-orange-500 hover:text-orange-700" title="Revoke License">
                                            <i class="fas fa-ban"></i>
                                        </button>` : ''
                                    }
                                    <button onclick="deleteLicense(${license.id})" class="action-btn text-red-500 hover:text-red-700" title="Delete License">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                    tbody.append(row);
                });
            }



            // Copy license key function (for table copy buttons) - ENHANCED
            window.copyLicenseKey = function(licenseKey) {
                if (!licenseKey) {
                    showErrorNotification('No license key provided to copy');
                    return;
                }

                console.log('Copying license key:', licenseKey.substring(0, 50) + '...');
                console.log('Full license key length:', licenseKey.length);

                navigator.clipboard.writeText(licenseKey).then(function() {
                    showSuccessNotification(`License key copied to clipboard! (${licenseKey.length} characters)`);
                    console.log('Successfully copied license key to clipboard');
                }).catch(function(err) {
                    console.error('Failed to copy license key:', err);
                    // Fallback method for older browsers
                    try {
                        const textArea = document.createElement('textarea');
                        textArea.value = licenseKey;
                        textArea.style.position = 'fixed';
                        textArea.style.opacity = '0';
                        document.body.appendChild(textArea);
                        textArea.select();
                        document.execCommand('copy');
                        document.body.removeChild(textArea);
                        showSuccessNotification(`License key copied to clipboard! (${licenseKey.length} characters)`);
                        console.log('Successfully copied license key using fallback method');
                    } catch (fallbackErr) {
                        console.error('Fallback copy method also failed:', fallbackErr);
                        showErrorNotification('Failed to copy license key. Please manually select and copy the key.');
                    }
                });
            };

            // Toggle license key display in table
            window.toggleLicenseKeyDisplay = function(element) {
                const fullKey = element.getAttribute('data-full-key');
                const currentText = element.textContent.trim();

                if (fullKey && currentText !== fullKey) {
                    // Show full key
                    element.textContent = fullKey;
                    element.title = 'Click to shorten key';
                } else if (fullKey) {
                    // Show shortened key
                    const shortKey = shortenLicenseKey(fullKey);
                    element.textContent = shortKey;
                    element.title = 'Click to view full key: ' + fullKey;
                }
            };

            // Legacy function for backward compatibility
            window.copyToClipboard = function(text) {
                navigator.clipboard.writeText(text).then(function() {
                    showSuccessNotification('License key copied to clipboard!');
                }).catch(function(err) {
                    console.error('Failed to copy text:', err);
                    showErrorNotification('Failed to copy to clipboard');
                });
            };

            // Suspend license function
            window.suspendLicense = function(licenseId) {
                if (confirm('Are you sure you want to suspend this license? The customer will no longer be able to use it.')) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'update_license',
                            license_id: licenseId,
                            status: 'suspended'
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showSuccessNotification('License suspended successfully!');
                                loadLicenses();
                                loadStats();
                            } else {
                                showErrorNotification(response.error);
                            }
                        },
                        error: function() {
                            showErrorNotification('Failed to suspend license. Please try again.');
                        }
                    });
                }
            };

            // Activate license function
            window.activateLicense = function(licenseId) {
                if (confirm('Are you sure you want to activate this license?')) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'update_license',
                            license_id: licenseId,
                            status: 'active'
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showSuccessNotification('License activated successfully!');
                                loadLicenses();
                                loadStats();
                            } else {
                                showErrorNotification(response.error);
                            }
                        },
                        error: function() {
                            showErrorNotification('Failed to activate license. Please try again.');
                        }
                    });
                }
            };

            // Delete license function
            window.deleteLicense = function(licenseId) {
                if (confirm('Are you sure you want to permanently delete this license? This action cannot be undone.')) {
                    $.ajax({
                        url: '',
                        method: 'POST',
                        data: {
                            action: 'delete_license',
                            license_id: licenseId
                        },
                        dataType: 'json',
                        success: function(response) {
                            if (response.success) {
                                showSuccessNotification('License deleted successfully!');
                                loadLicenses();
                                loadStats();
                            } else {
                                showErrorNotification(response.error);
                            }
                        },
                        error: function() {
                            showErrorNotification('Failed to delete license. Please try again.');
                        }
                    });
                }
            };

            // Revoke license function
            window.revokeLicense = function(licenseId) {
                const reason = prompt('Please enter a reason for revoking this license (optional):');
                if (reason !== null) { // User didn't cancel
                    if (confirm('Are you sure you want to revoke this license? The customer will no longer be able to use it.')) {
                        $.ajax({
                            url: '',
                            method: 'POST',
                            data: {
                                action: 'revoke_license',
                                license_id: licenseId,
                                reason: reason
                            },
                            dataType: 'json',
                            success: function(response) {
                                if (response.success) {
                                    showSuccessNotification('License revoked successfully!');
                                    loadLicenses();
                                    loadStats();
                                } else {
                                    showErrorNotification(response.error);
                                }
                            },
                            error: function() {
                                showErrorNotification('Failed to revoke license. Please try again.');
                            }
                        });
                    }
                }
            };

            // Copy license key button - FIXED: Always use full key
            $('#copyLicenseKey').click(function() {
                const licenseKey = $('#newLicenseKey').attr('data-full-key');
                if (!licenseKey) {
                    showErrorNotification('No license key available to copy');
                    return;
                }
                console.log('Copying license key:', licenseKey);
                console.log('License key length:', licenseKey.length);
                copyLicenseKey(licenseKey);
            });

            // Toggle license key display
            $(document).on('click', '#newLicenseKey', function() {
                const $this = $(this);
                const fullKey = $this.attr('data-full-key');
                const currentText = $this.text();

                if (fullKey && currentText !== fullKey) {
                    // Show full key
                    $this.text(fullKey);
                    $this.next('.text-xs').text('Click to shorten key');
                } else if (fullKey) {
                    // Show shortened key
                    const shortKey = shortenLicenseKey(fullKey);
                    $this.text(shortKey);
                    $this.next('.text-xs').text('Click to view full key');
                }
            });

            // Email license key button
            $('#emailLicenseKey').click(function() {
                const licenseKey = $('#newLicenseKey').attr('data-full-key') || $('#newLicenseKey').text();
                const email = $('#customerEmail').val();
                const name = $('#customerName').val();
                
                const subject = encodeURIComponent('Your Graph Search License Key');
                const body = encodeURIComponent(`Dear ${name},\n\nYour license key is: ${licenseKey}\n\nThank you for your purchase!`);
                
                window.open(`mailto:${email}?subject=${subject}&body=${body}`, '_blank');
            });

            // Export functionality
            $('#exportLicenses').click(function() {
                alert('Export functionality would generate an Excel file with all licenses.');
            });

            // Refresh functionality
            $('#refreshLicenses').click(function() {
                loadLicenses();
                loadStats();
                showSuccessNotification('Licenses refreshed successfully!');
            });

            // Upgrade checkbox handler
            $('#isUpgrade').change(function() {
                if ($(this).is(':checked')) {
                    $('#upgradeWarning').removeClass('hidden');
                } else {
                    $('#upgradeWarning').addClass('hidden');
                }
            });

            // Notification functions
            function showErrorNotification(message) {
                // Remove any existing notifications
                $('.notification').remove();

                const notification = $(`
                    <div class="notification fixed top-4 right-4 bg-red-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <div class="flex-1">
                                <div class="font-semibold">Error</div>
                                <div class="text-sm">${message}</div>
                            </div>
                            <button onclick="$(this).parent().parent().fadeOut()" class="ml-4 text-white hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `);

                $('body').append(notification);

                // Auto-hide after 8 seconds
                setTimeout(() => {
                    notification.fadeOut();
                }, 8000);
            }

            function showSuccessNotification(message) {
                // Remove any existing notifications
                $('.notification').remove();

                const notification = $(`
                    <div class="notification fixed top-4 right-4 bg-green-500 text-white px-6 py-4 rounded-lg shadow-lg z-50 max-w-md">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-3"></i>
                            <div class="flex-1">
                                <div class="font-semibold">Success</div>
                                <div class="text-sm">${message}</div>
                            </div>
                            <button onclick="$(this).parent().parent().fadeOut()" class="ml-4 text-white hover:text-gray-200">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                `);

                $('body').append(notification);

                // Auto-hide after 4 seconds
                setTimeout(() => {
                    notification.fadeOut();
                }, 4000);
            }

            // Sidebar toggle for mobile
            document.getElementById('sidebarToggle').addEventListener('click', function() {
                const sidebar = document.getElementById('sidebar');
                sidebar.classList.toggle('hidden');
            });
        });
    </script>
</body>
</html>
