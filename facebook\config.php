<?php
/**
 * Facebook Graph Search Configuration
 * 
 * Centralized configuration for the Facebook Graph Search module
 */

// Prevent direct access
if (!defined('FACEBOOK_MODULE_LOADED')) {
    define('FACEBOOK_MODULE_LOADED', true);
}

// Environment configuration
define('FB_ENVIRONMENT', 'development'); // development, staging, production

// Database Configuration for Facebook module
define('FB_DB_HOST', 'localhost');
define('FB_DB_NAME', 'graphFB');
define('FB_DB_USER', 'root');
define('FB_DB_PASS', '');
define('FB_DB_CHARSET', 'utf8mb4');
define('FB_DB_PORT', 3306);

/**
 * Generate dynamic base URL for Facebook module
 */
function generateFacebookBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // Get the current script directory relative to document root
    $scriptDir = dirname($_SERVER['SCRIPT_NAME']);
    
    // Remove any trailing slashes
    $basePath = rtrim($scriptDir, '/');
    
    // If we're not already in a facebook path, append it
    if (!str_contains($basePath, '/facebook')) {
        $basePath .= '/facebook';
    }
    
    return $protocol . '://' . $host . $basePath;
}

/**
 * Generate dynamic root URL
 */
function generateFacebookRootUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    
    // Get the current script directory relative to document root
    $scriptDir = dirname($_SERVER['SCRIPT_NAME']);
    
    // Remove facebook from the path to get root
    $basePath = rtrim($scriptDir, '/');
    $basePath = str_replace('/facebook', '', $basePath);
    
    return $protocol . '://' . $host . $basePath;
}

// Facebook Application Configuration
$FB_APP_CONFIG = [
    'app_name' => 'Facebook Graph Search System',
    'app_version' => '1.0.0',
    'app_url' => generateFacebookBaseUrl(),
    'root_url' => generateFacebookRootUrl(),
    'admin_url' => generateFacebookBaseUrl() . '/admin',
    'timezone' => 'UTC',
    'debug' => FB_ENVIRONMENT === 'development',
    'log_level' => FB_ENVIRONMENT === 'development' ? 'debug' : 'error'
];

// Security Configuration
$FB_SECURITY_CONFIG = [
    // Session settings
    'session_timeout' => 3600, // 1 hour in seconds
    'remember_me_timeout' => 2592000, // 30 days in seconds
    
    // Rate limiting
    'rate_limit_window' => 300, // 5 minutes
    'rate_limit_max_attempts' => 10,
    
    // License validation
    'license_cache_duration' => 300, // 5 minutes
    'device_limit_default' => 3,
    'trial_period_days' => 7
];

/**
 * Get Facebook configuration value
 * 
 * @param string $section Configuration section (app, security)
 * @param string $key Configuration key
 * @param mixed $default Default value if key not found
 * @return mixed Configuration value
 */
function getFacebookConfig($section, $key = null, $default = null) {
    global $FB_APP_CONFIG, $FB_SECURITY_CONFIG;
    
    $configs = [
        'app' => $FB_APP_CONFIG,
        'security' => $FB_SECURITY_CONFIG
    ];
    
    if (!isset($configs[$section])) {
        return $default;
    }
    
    if ($key === null) {
        return $configs[$section];
    }
    
    return isset($configs[$section][$key]) ? $configs[$section][$key] : $default;
}

/**
 * Get Facebook database connection
 * 
 * @return PDO Database connection
 * @throws Exception If connection fails
 */
function getFacebookDatabase() {
    static $pdo = null;
    
    if ($pdo === null) {
        try {
            $dsn = sprintf(
                "mysql:host=%s;port=%d;dbname=%s;charset=%s",
                FB_DB_HOST,
                FB_DB_PORT,
                FB_DB_NAME,
                FB_DB_CHARSET
            );
            
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::ATTR_PERSISTENT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . FB_DB_CHARSET . " COLLATE " . FB_DB_CHARSET . "_unicode_ci"
            ];
            
            $pdo = new PDO($dsn, FB_DB_USER, FB_DB_PASS, $options);
            
            // Set SQL mode for strict data handling
            $pdo->exec("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");
            
        } catch (PDOException $e) {
            throw new Exception("Facebook database connection failed: " . $e->getMessage());
        }
    }
    
    return $pdo;
}

/**
 * Log Facebook module errors
 */
function logFacebookError($message, $context = []) {
    $logFile = __DIR__ . '/logs/facebook.log';
    $logDir = dirname($logFile);
    
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    $logEntry = "[$timestamp] ERROR: $message$contextStr" . PHP_EOL;
    
    file_put_contents($logFile, $logEntry, FILE_APPEND | LOCK_EX);
}
?>
