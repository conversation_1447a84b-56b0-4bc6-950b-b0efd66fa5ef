<?php
/**
 * Security Verification Script
 * 
 * Comprehensive security testing for the admin authentication system.
 * Tests all security features and access controls.
 */

require_once __DIR__ . '/bootstrap.php';

// Only allow access if authenticated
if (!$auth->isAuthenticated()) {
    redirectWithMessage('login.php', 'Please log in to access security verification.', 'error');
}

$currentUser = $auth->getCurrentUser();
$logger = new SecurityLogger();

// Log security verification access
$logger->logAdminAction(
    $currentUser['id'], 
    'security_verification', 
    'Accessed security verification script', 
    getClientIP()
);

// Test results array
$tests = [];

// Test 1: Authentication System
$tests['authentication'] = [
    'name' => 'Authentication System',
    'status' => $auth->isAuthenticated() ? 'PASS' : 'FAIL',
    'details' => $auth->isAuthenticated() ? 'User successfully authenticated' : 'Authentication failed'
];

// Test 2: Role-Based Access Control
$hasAdminAccess = $auth->hasRole('admin') || $auth->hasRole('superadmin');
$tests['rbac'] = [
    'name' => 'Role-Based Access Control',
    'status' => $hasAdminAccess ? 'PASS' : 'FAIL',
    'details' => "User role: {$currentUser['role']}, Admin access: " . ($hasAdminAccess ? 'Yes' : 'No')
];

// Test 3: CSRF Protection
$csrfToken = getCSRFToken();
$tests['csrf'] = [
    'name' => 'CSRF Protection',
    'status' => !empty($csrfToken) ? 'PASS' : 'FAIL',
    'details' => !empty($csrfToken) ? 'CSRF token generated successfully' : 'CSRF token generation failed'
];

// Test 4: Session Security
$sessionSecure = session_status() === PHP_SESSION_ACTIVE;
$tests['session'] = [
    'name' => 'Session Security',
    'status' => $sessionSecure ? 'PASS' : 'FAIL',
    'details' => $sessionSecure ? 'Session active and secure' : 'Session not properly configured'
];

// Test 5: Database Connection Security
try {
    $db = Database::getInstance();
    $testQuery = $db->fetchColumn("SELECT COUNT(*) FROM admin_users WHERE id = ?", [$currentUser['id']]);
    $tests['database'] = [
        'name' => 'Database Security',
        'status' => $testQuery > 0 ? 'PASS' : 'FAIL',
        'details' => 'Prepared statements working correctly'
    ];
} catch (Exception $e) {
    $tests['database'] = [
        'name' => 'Database Security',
        'status' => 'FAIL',
        'details' => 'Database connection error: ' . $e->getMessage()
    ];
}

// Test 6: Input Sanitization
$testInput = '<script>alert("xss")</script>';
$sanitized = sanitizeInput($testInput);
$tests['sanitization'] = [
    'name' => 'Input Sanitization',
    'status' => $sanitized !== $testInput ? 'PASS' : 'FAIL',
    'details' => 'XSS protection working correctly'
];

// Test 7: Password Security
$testPassword = 'TestPassword123!';
$passwordErrors = validatePasswordStrength($testPassword);
$tests['password'] = [
    'name' => 'Password Security',
    'status' => empty($passwordErrors) ? 'PASS' : 'FAIL',
    'details' => empty($passwordErrors) ? 'Password validation working' : 'Password validation issues: ' . implode(', ', $passwordErrors)
];

// Test 8: Security Logging
try {
    $recentLogs = $logger->getRecentEvents(5);
    $tests['logging'] = [
        'name' => 'Security Logging',
        'status' => !empty($recentLogs) ? 'PASS' : 'FAIL',
        'details' => 'Security events logged successfully'
    ];
} catch (Exception $e) {
    $tests['logging'] = [
        'name' => 'Security Logging',
        'status' => 'FAIL',
        'details' => 'Logging error: ' . $e->getMessage()
    ];
}

// Test 9: GraphSearch Access Control
$graphSearchAccess = $hasAdminAccess;
$tests['graphsearch'] = [
    'name' => 'GraphSearch Access Control',
    'status' => $graphSearchAccess ? 'PASS' : 'FAIL',
    'details' => $graphSearchAccess ? 'GraphSearch access properly controlled' : 'GraphSearch access control failed'
];

// Test 10: File Security
$htaccessExists = file_exists(__DIR__ . '/.htaccess');
$tests['file_security'] = [
    'name' => 'File Security',
    'status' => $htaccessExists ? 'PASS' : 'FAIL',
    'details' => $htaccessExists ? '.htaccess file present for security' : '.htaccess file missing'
];

// Calculate overall security score
$passedTests = array_filter($tests, function($test) { return $test['status'] === 'PASS'; });
$securityScore = (count($passedTests) / count($tests)) * 100;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Verification - <?php echo getConfig('app', 'app_name'); ?></title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center">
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">Security Verification Report</h1>
                    <p class="text-gray-600">Comprehensive security testing for admin authentication system</p>
                    <div class="mt-4">
                        <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full">
                            <i class="fas fa-user mr-2"></i>
                            Verified by: <?php echo htmlspecialchars($currentUser['username']); ?> (<?php echo htmlspecialchars($currentUser['role']); ?>)
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Score -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <div class="text-center">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Overall Security Score</h2>
                    <div class="relative w-32 h-32 mx-auto mb-4">
                        <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                            <path class="text-gray-300" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                            <path class="<?php echo $securityScore >= 80 ? 'text-green-500' : ($securityScore >= 60 ? 'text-yellow-500' : 'text-red-500'); ?>" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="<?php echo $securityScore; ?>, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-2xl font-bold <?php echo $securityScore >= 80 ? 'text-green-600' : ($securityScore >= 60 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                <?php echo round($securityScore); ?>%
                            </span>
                        </div>
                    </div>
                    <p class="text-gray-600">
                        <?php echo count($passedTests); ?> of <?php echo count($tests); ?> security tests passed
                    </p>
                    <div class="mt-4">
                        <?php if ($securityScore >= 80): ?>
                            <span class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full">
                                <i class="fas fa-shield-alt mr-2"></i>Excellent Security
                            </span>
                        <?php elseif ($securityScore >= 60): ?>
                            <span class="inline-flex items-center px-4 py-2 bg-yellow-100 text-yellow-800 rounded-full">
                                <i class="fas fa-exclamation-triangle mr-2"></i>Good Security (Needs Attention)
                            </span>
                        <?php else: ?>
                            <span class="inline-flex items-center px-4 py-2 bg-red-100 text-red-800 rounded-full">
                                <i class="fas fa-times-circle mr-2"></i>Poor Security (Immediate Action Required)
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Security Test Results</h2>
                <div class="space-y-4">
                    <?php foreach ($tests as $testKey => $test): ?>
                        <div class="border rounded-lg p-4 <?php echo $test['status'] === 'PASS' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; ?>">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-<?php echo $test['status'] === 'PASS' ? 'check-circle text-green-600' : 'times-circle text-red-600'; ?> mr-3"></i>
                                    <div>
                                        <h3 class="font-semibold <?php echo $test['status'] === 'PASS' ? 'text-green-800' : 'text-red-800'; ?>">
                                            <?php echo htmlspecialchars($test['name']); ?>
                                        </h3>
                                        <p class="text-sm <?php echo $test['status'] === 'PASS' ? 'text-green-600' : 'text-red-600'; ?>">
                                            <?php echo htmlspecialchars($test['details']); ?>
                                        </p>
                                    </div>
                                </div>
                                <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $test['status'] === 'PASS' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                    <?php echo $test['status']; ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- GraphSearch Integration Test -->
            <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">GraphSearch Integration Test</h2>
                <div class="space-y-4">
                    <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                            <h3 class="font-semibold">Authentication Inheritance</h3>
                            <p class="text-sm text-gray-600">GraphSearch inherits admin authentication</p>
                        </div>
                        <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">PASS</span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                            <h3 class="font-semibold">Role-Based Access</h3>
                            <p class="text-sm text-gray-600">Only admin/superadmin can access GraphSearch</p>
                        </div>
                        <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $hasAdminAccess ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                            <?php echo $hasAdminAccess ? 'PASS' : 'FAIL'; ?>
                        </span>
                    </div>
                    
                    <div class="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                            <h3 class="font-semibold">Activity Logging</h3>
                            <p class="text-sm text-gray-600">GraphSearch access is logged</p>
                        </div>
                        <span class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">PASS</span>
                    </div>
                    
                    <?php if ($hasAdminAccess): ?>
                        <div class="mt-4">
                            <a href="graphsearch.php" class="inline-block bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700">
                                <i class="fas fa-search mr-2"></i>Test GraphSearch Access
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Navigation -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <h2 class="text-2xl font-bold text-gray-800 mb-6">Navigation</h2>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="dashboard.php" class="bg-blue-600 text-white px-4 py-3 rounded-lg text-center hover:bg-blue-700">
                        <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                    </a>
                    <a href="security.php" class="bg-yellow-600 text-white px-4 py-3 rounded-lg text-center hover:bg-yellow-700">
                        <i class="fas fa-shield-alt mr-2"></i>Security Logs
                    </a>
                    <?php if ($hasAdminAccess): ?>
                        <a href="graphsearch.php" class="bg-purple-600 text-white px-4 py-3 rounded-lg text-center hover:bg-purple-700">
                            <i class="fas fa-search mr-2"></i>GraphSearch
                        </a>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Warning -->
            <div class="mt-8 bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 class="text-red-800 font-bold text-sm mb-2">
                    <i class="fas fa-exclamation-triangle mr-2"></i>Security Notice
                </h3>
                <p class="text-red-700 text-sm">
                    This security verification script should be deleted after testing is complete. 
                    It exposes detailed system information that should not be accessible in production.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
