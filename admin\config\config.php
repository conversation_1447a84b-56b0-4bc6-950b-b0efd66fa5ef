<?php
/**
 * GraphDB Admin Authentication System Configuration
 * 
 * Security Notice: This file contains sensitive configuration data.
 * Ensure this file is not accessible via web browser.
 */

// Prevent direct access
if (!defined('ADMIN_SYSTEM_LOADED')) {
    die('Direct access not allowed');
}

// Environment configuration
define('ENVIRONMENT', 'development'); // development, staging, production

// Database Configuration
define('DB_HOST', 'localhost');
define('DB_NAME', 'graphDB');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');
define('DB_PORT', 3306);

// Application Configuration
$APP_CONFIG = [
    'app_name' => 'GraphDB Admin System',
    'app_version' => '1.0.0',
    'app_url' => 'http://localhost/linked/admin',
    'timezone' => 'UTC',
    'debug' => ENVIRONMENT === 'development',
    'log_level' => ENVIRONMENT === 'development' ? 'debug' : 'error'
];

// Security Configuration
$SECURITY_CONFIG = [
    // Session settings
    'session_timeout' => 3600, // 1 hour in seconds
    'remember_me_timeout' => 2592000, // 30 days in seconds
    'session_regenerate_interval' => 300, // 5 minutes
    
    // Password requirements
    'password_min_length' => 8,
    'password_require_uppercase' => true,
    'password_require_lowercase' => true,
    'password_require_numbers' => true,
    'password_require_special' => true,
    'password_special_chars' => '!@#$%^&*()_+-=[]{}|;:,.<>?',
    
    // Account lockout settings
    'max_failed_attempts' => 5,
    'lockout_duration' => 900, // 15 minutes in seconds
    'lockout_progressive' => true, // Increase lockout time with repeated failures
    
    // Rate limiting
    'rate_limit_window' => 300, // 5 minutes
    'rate_limit_max_attempts' => 10,
    
    // 2FA settings
    '2fa_issuer' => 'GraphDB Admin',
    '2fa_window' => 2, // Allow 2 time windows (30 seconds each)
    
    // Password reset
    'reset_token_expiry' => 3600, // 1 hour
    'reset_max_attempts' => 3,
    
    // CSRF protection
    'csrf_token_expiry' => 3600,
    'csrf_regenerate_interval' => 300,
    
    // Security headers
    'security_headers' => [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Strict-Transport-Security' => 'max-age=********; includeSubDomains',
        'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline' cdn.tailwindcss.com cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' cdn.tailwindcss.com cdnjs.cloudflare.com; img-src 'self' data:; font-src 'self' cdnjs.cloudflare.com; connect-src 'self';"
    ]
];

// Email Configuration
$EMAIL_CONFIG = [
    'smtp_enabled' => true, // Set to true to enable SMTP
    'smtp_host' => 'smtp.hostinger.com',
    'smtp_port' => 465,
    'smtp_username' => '<EMAIL>',
    'smtp_password' => 'W+YGx$KY=8r',
    'smtp_encryption' => 'ssl', // tls or ssl
    'from_email' => '<EMAIL>',
    'from_name' => 'AI Graph Search'
];

// Logging Configuration
$LOG_CONFIG = [
    'log_file' => __DIR__ . '/../logs/admin.log',
    'max_file_size' => 10485760, // 10MB
    'max_files' => 5,
    'log_rotation' => true
];

/**
 * Get configuration value
 * 
 * @param string $section Configuration section (app, security, email, log)
 * @param string $key Configuration key
 * @param mixed $default Default value if key not found
 * @return mixed Configuration value
 */
function getConfig($section, $key = null, $default = null) {
    global $APP_CONFIG, $SECURITY_CONFIG, $EMAIL_CONFIG, $LOG_CONFIG;
    
    $configs = [
        'app' => $APP_CONFIG,
        'security' => $SECURITY_CONFIG,
        'email' => $EMAIL_CONFIG,
        'log' => $LOG_CONFIG
    ];
    
    if (!isset($configs[$section])) {
        return $default;
    }
    
    if ($key === null) {
        return $configs[$section];
    }
    
    return isset($configs[$section][$key]) ? $configs[$section][$key] : $default;
}

/**
 * Emergency admin credentials
 * These should be changed in production
 */
define('EMERGENCY_ADMIN_USERNAME', 'superadmin');
define('EMERGENCY_ADMIN_PASSWORD', 'SuperAdmin2024!');

/**
 * Encryption key for sensitive data
 * Generate a new key for production: openssl_rand_pseudo_bytes(32)
 */
define('ENCRYPTION_KEY', 'GraphDBAdminSystem2024SecretKey!');

/**
 * Session configuration (only if session not started)
 */
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) ? 1 : 0);
    ini_set('session.use_strict_mode', 1);
    ini_set('session.cookie_samesite', 'Strict');
}

/**
 * Error reporting based on environment
 */
if (ENVIRONMENT === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    ini_set('log_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);
}

/**
 * Set timezone
 */
date_default_timezone_set(getConfig('app', 'timezone', 'UTC'));
?>
