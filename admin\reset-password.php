<?php
/**
 * Reset Password Page
 * 
 * Handles password reset completion with token validation
 * and password strength requirements.
 */

require_once __DIR__ . '/bootstrap.php';

// Redirect if already authenticated
if ($auth->isAuthenticated()) {
    header('Location: dashboard.php');
    exit;
}

$error = '';
$success = '';
$token = sanitizeInput($_GET['token'] ?? '');

if (empty($token)) {
    redirectWithMessage('forgot-password.php', 'Invalid reset link. Please request a new password reset.', 'error');
}

// Verify token exists and is valid
try {
    $db = Database::getInstance();
    $resetToken = $db->fetchRow(
        "SELECT * FROM password_reset_tokens WHERE token = :token AND used = 0 AND expires_at > NOW()",
        [':token' => $token]
    );
    
    if (!$resetToken) {
        redirectWithMessage('forgot-password.php', 'This reset link has expired or is invalid. Please request a new password reset.', 'error');
    }
} catch (Exception $e) {
    redirectWithMessage('forgot-password.php', 'An error occurred. Please try again.', 'error');
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Validate CSRF token
        if (!validateCSRFToken($_POST['csrf_token'] ?? '')) {
            throw new Exception('Invalid security token. Please try again.');
        }
        
        $newPassword = $_POST['new_password'] ?? '';
        $confirmPassword = $_POST['confirm_password'] ?? '';
        
        if (empty($newPassword) || empty($confirmPassword)) {
            throw new Exception('Please enter and confirm your new password.');
        }
        
        if ($newPassword !== $confirmPassword) {
            throw new Exception('Passwords do not match.');
        }
        
        // Validate password strength
        $passwordErrors = validatePasswordStrength($newPassword);
        if (!empty($passwordErrors)) {
            throw new Exception(implode(' ', $passwordErrors));
        }
        
        // Reset password
        $auth->resetPassword($token, $newPassword);
        
        redirectWithMessage('login.php', 'Your password has been reset successfully. Please log in with your new password.', 'success');
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Password - <?php echo getConfig('app', 'app_name'); ?></title>
    <link href="https://cdn.tailwindcss.com/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .login-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .input-group {
            position: relative;
        }
        .input-group input {
            padding-left: 2.5rem;
        }
        .input-group .icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #6b7280;
        }
        .password-strength {
            margin-top: 0.5rem;
        }
        .strength-bar {
            height: 4px;
            background: #e5e7eb;
            border-radius: 2px;
            overflow: hidden;
        }
        .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
        }
        .strength-weak { background: #ef4444; width: 25%; }
        .strength-fair { background: #f59e0b; width: 50%; }
        .strength-good { background: #10b981; width: 75%; }
        .strength-strong { background: #059669; width: 100%; }
    </style>
</head>
<body class="login-container">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="login-card rounded-lg shadow-2xl p-8 w-full max-w-md">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-lock text-white text-2xl"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-800">Reset Password</h1>
                <p class="text-gray-600 mt-2">Enter your new password</p>
            </div>

            <!-- Error Messages -->
            <?php if ($error): ?>
                <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                    <i class="fas fa-exclamation-circle mr-2"></i><?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <!-- Reset Form -->
            <form method="POST" id="resetForm">
                <input type="hidden" name="csrf_token" value="<?php echo getCSRFToken(); ?>">
                
                <!-- New Password -->
                <div class="mb-4">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        New Password
                    </label>
                    <div class="input-group">
                        <i class="icon fas fa-lock"></i>
                        <input type="password" name="new_password" id="newPassword"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Enter new password" required>
                    </div>
                    <div class="password-strength">
                        <div class="strength-bar">
                            <div class="strength-fill" id="strengthBar"></div>
                        </div>
                        <p class="text-xs mt-1" id="strengthText">Password strength: <span id="strengthLevel">-</span></p>
                    </div>
                </div>

                <!-- Confirm Password -->
                <div class="mb-6">
                    <label class="block text-gray-700 text-sm font-bold mb-2">
                        Confirm Password
                    </label>
                    <div class="input-group">
                        <i class="icon fas fa-lock"></i>
                        <input type="password" name="confirm_password" id="confirmPassword"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
                               placeholder="Confirm new password" required>
                    </div>
                    <p class="text-xs text-gray-500 mt-1" id="matchText"></p>
                </div>

                <!-- Password Requirements -->
                <div class="mb-6 p-4 bg-gray-50 border border-gray-200 rounded-lg">
                    <h3 class="text-gray-800 font-bold text-sm mb-2">Password Requirements:</h3>
                    <ul class="text-gray-600 text-xs space-y-1" id="requirements">
                        <li id="req-length">• At least <?php echo getConfig('security', 'password_min_length'); ?> characters</li>
                        <li id="req-uppercase">• At least one uppercase letter</li>
                        <li id="req-lowercase">• At least one lowercase letter</li>
                        <li id="req-number">• At least one number</li>
                        <li id="req-special">• At least one special character</li>
                    </ul>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="submitBtn" class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-2 px-4 rounded-lg transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    <i class="fas fa-save mr-2"></i>Reset Password
                </button>
            </form>

            <!-- Additional Links -->
            <div class="mt-6 text-center">
                <a href="login.php" class="text-blue-600 hover:text-blue-800 text-sm">
                    <i class="fas fa-arrow-left mr-1"></i>Back to Login
                </a>
            </div>

            <!-- Footer -->
            <div class="mt-8 text-center text-xs text-gray-500">
                <p>&copy; <?php echo date('Y'); ?> <?php echo getConfig('app', 'app_name'); ?></p>
            </div>
        </div>
    </div>

    <script>
        const newPasswordInput = document.getElementById('newPassword');
        const confirmPasswordInput = document.getElementById('confirmPassword');
        const strengthBar = document.getElementById('strengthBar');
        const strengthLevel = document.getElementById('strengthLevel');
        const matchText = document.getElementById('matchText');
        const submitBtn = document.getElementById('submitBtn');

        // Password strength checker
        function checkPasswordStrength(password) {
            let score = 0;
            let feedback = [];

            // Length check
            if (password.length >= <?php echo getConfig('security', 'password_min_length'); ?>) {
                score += 1;
                document.getElementById('req-length').classList.add('text-green-600');
            } else {
                document.getElementById('req-length').classList.remove('text-green-600');
            }

            // Uppercase check
            if (/[A-Z]/.test(password)) {
                score += 1;
                document.getElementById('req-uppercase').classList.add('text-green-600');
            } else {
                document.getElementById('req-uppercase').classList.remove('text-green-600');
            }

            // Lowercase check
            if (/[a-z]/.test(password)) {
                score += 1;
                document.getElementById('req-lowercase').classList.add('text-green-600');
            } else {
                document.getElementById('req-lowercase').classList.remove('text-green-600');
            }

            // Number check
            if (/[0-9]/.test(password)) {
                score += 1;
                document.getElementById('req-number').classList.add('text-green-600');
            } else {
                document.getElementById('req-number').classList.remove('text-green-600');
            }

            // Special character check
            if (/[<?php echo preg_quote(getConfig('security', 'password_special_chars'), '/'); ?>]/.test(password)) {
                score += 1;
                document.getElementById('req-special').classList.add('text-green-600');
            } else {
                document.getElementById('req-special').classList.remove('text-green-600');
            }

            // Update strength bar
            strengthBar.className = 'strength-fill';
            if (score === 0) {
                strengthLevel.textContent = '-';
            } else if (score <= 2) {
                strengthBar.classList.add('strength-weak');
                strengthLevel.textContent = 'Weak';
            } else if (score <= 3) {
                strengthBar.classList.add('strength-fair');
                strengthLevel.textContent = 'Fair';
            } else if (score <= 4) {
                strengthBar.classList.add('strength-good');
                strengthLevel.textContent = 'Good';
            } else {
                strengthBar.classList.add('strength-strong');
                strengthLevel.textContent = 'Strong';
            }

            return score >= 5;
        }

        // Check password match
        function checkPasswordMatch() {
            const password = newPasswordInput.value;
            const confirm = confirmPasswordInput.value;

            if (confirm === '') {
                matchText.textContent = '';
                matchText.className = 'text-xs text-gray-500 mt-1';
                return false;
            }

            if (password === confirm) {
                matchText.textContent = '✓ Passwords match';
                matchText.className = 'text-xs text-green-600 mt-1';
                return true;
            } else {
                matchText.textContent = '✗ Passwords do not match';
                matchText.className = 'text-xs text-red-600 mt-1';
                return false;
            }
        }

        // Update submit button state
        function updateSubmitButton() {
            const isStrong = checkPasswordStrength(newPasswordInput.value);
            const isMatch = checkPasswordMatch();
            
            submitBtn.disabled = !(isStrong && isMatch && newPasswordInput.value.length > 0);
        }

        // Event listeners
        newPasswordInput.addEventListener('input', updateSubmitButton);
        confirmPasswordInput.addEventListener('input', updateSubmitButton);

        // Initial state
        updateSubmitButton();

        // Prevent form resubmission on page refresh
        if (window.history.replaceState) {
            window.history.replaceState(null, null, window.location.href);
        }
    </script>
</body>
</html>
