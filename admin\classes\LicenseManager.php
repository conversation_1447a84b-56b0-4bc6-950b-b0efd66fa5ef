<?php
/**
 * License Manager Class
 * 
 * Handles all license-related operations including generation, validation,
 * storage, and management for the GraphSearch application.
 */

class LicenseManager {
    private $db;
    private $licenseSecret;
    
    public function __construct($database = null) {
        if ($database) {
            $this->db = $database;
        } else {
            // Simple PDO connection
            try {
                $this->db = new PDO(
                    "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET,
                    DB_USER,
                    DB_PASS,
                    [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]
                );
            } catch (PDOException $e) {
                throw new Exception("Database connection failed: " . $e->getMessage());
            }
        }
        $this->licenseSecret = 'ELITEAPPSUITE2024LINKEDIN';
    }
    
    /**
     * Generate a new license
     */
    public function generateLicense($data) {
        try {
            // Validate input data
            $this->validateLicenseData($data);

            // Check if email already has an active license
            $existingLicense = $this->getLicenseByEmail($data['customer_email']);
            if ($existingLicense) {
                $expiryDate = new DateTime($existingLicense['expires_at']);
                $now = new DateTime();

                if ($existingLicense['status'] === 'active' && $expiryDate > $now) {
                    // Check if this is an upgrade request
                    if (isset($data['is_upgrade']) && $data['is_upgrade'] === true) {
                        // Validate that this is actually an upgrade (higher tier)
                        if (!$this->isLicenseUpgrade($existingLicense['license_type'], $data['license_type'])) {
                            throw new Exception('Invalid upgrade: ' . ucfirst($data['license_type']) . ' is not a higher tier than the existing ' . ucfirst($existingLicense['license_type']) . ' license. Please select a higher tier license.');
                        }

                        // Mark existing license as superseded
                        $this->updateLicense($existingLicense['id'], ['status' => 'superseded']);

                        // Log the upgrade
                        error_log("License upgrade: {$existingLicense['license_type']} -> {$data['license_type']} for {$data['customer_email']}");
                    } else {
                        throw new Exception('An active license is already assigned to this email address: ' . $data['customer_email'] . '. The existing license expires on ' . $expiryDate->format('M j, Y') . '. To upgrade this license, please check the "License Upgrade" option.');
                    }
                }
            }
            
            // Calculate expiry date
            $expiryDate = new DateTime();
            $expiryDate->add(new DateInterval('P' . $data['duration_months'] . 'M'));
            
            // Generate cryptographic license key
            $licenseKey = $this->generateLicenseKey($data, $expiryDate);
            
            // Prepare license data for database
            $licenseData = [
                'license_key' => $licenseKey,
                'customer_name' => $data['customer_name'],
                'customer_email' => $data['customer_email'],
                'license_type' => $data['license_type'],
                'max_devices' => $data['max_devices'] ?? 3,
                'duration_months' => $data['duration_months'],
                'expires_at' => $expiryDate->format('Y-m-d H:i:s'),
                'notes' => $data['notes'] ?? null,
                'created_by' => $data['created_by'] ?? null,
                'license_data' => json_encode([
                    'type' => $data['license_type'],
                    'email' => $data['customer_email'],
                    'generated_date' => date('Y-m-d H:i:s'),
                    'version' => '2.0'
                ])
            ];
            
            // Insert into database
            $sql = "INSERT INTO licenses (license_key, customer_name, customer_email, license_type,
                    max_devices, duration_months, expires_at, notes, created_by, license_data)
                    VALUES (:license_key, :customer_name, :customer_email, :license_type,
                    :max_devices, :duration_months, :expires_at, :notes, :created_by, :license_data)";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($licenseData);

            $licenseId = $this->db->lastInsertId();
            
            // Return complete license information
            return [
                'id' => $licenseId,
                'license_key' => $licenseKey,
                'customer_name' => $data['customer_name'],
                'customer_email' => $data['customer_email'],
                'license_type' => $data['license_type'],
                'status' => 'active',
                'expires_at' => $expiryDate->format('Y-m-d H:i:s'),
                'created_at' => date('Y-m-d H:i:s')
            ];
            
        } catch (Exception $e) {
            throw new Exception('Failed to generate license: ' . $e->getMessage());
        }
    }
    
    /**
     * Get all licenses with optional filtering
     */
    public function getLicenses($filters = []) {
        try {
            $sql = "SELECT l.*, au.username as created_by_username 
                    FROM licenses l 
                    LEFT JOIN admin_users au ON l.created_by = au.id";
            
            $conditions = [];
            $params = [];
            
            // Apply filters
            if (!empty($filters['status'])) {
                $conditions[] = "l.status = :status";
                $params['status'] = $filters['status'];
            }
            
            if (!empty($filters['license_type'])) {
                $conditions[] = "l.license_type = :license_type";
                $params['license_type'] = $filters['license_type'];
            }
            
            if (!empty($filters['customer_email'])) {
                $conditions[] = "l.customer_email LIKE :customer_email";
                $params['customer_email'] = '%' . $filters['customer_email'] . '%';
            }
            
            if (!empty($filters['search'])) {
                $conditions[] = "(l.customer_name LIKE :search OR l.customer_email LIKE :search OR l.license_key LIKE :search)";
                $params['search'] = '%' . $filters['search'] . '%';
            }
            
            if (!empty($conditions)) {
                $sql .= " WHERE " . implode(" AND ", $conditions);
            }
            
            $sql .= " ORDER BY l.created_at DESC";
            
            // Add pagination if specified
            if (!empty($filters['limit'])) {
                $sql .= " LIMIT " . intval($filters['limit']);
                if (!empty($filters['offset'])) {
                    $sql .= " OFFSET " . intval($filters['offset']);
                }
            }
            
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(PDO::FETCH_ASSOC);
            
        } catch (Exception $e) {
            throw new Exception('Failed to retrieve licenses: ' . $e->getMessage());
        }
    }
    
    /**
     * Get license by ID
     */
    public function getLicenseById($id) {
        try {
            $sql = "SELECT l.*, au.username as created_by_username
                    FROM licenses l
                    LEFT JOIN admin_users au ON l.created_by = au.id
                    WHERE l.id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->execute(['id' => $id]);

            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            throw new Exception('Failed to retrieve license: ' . $e->getMessage());
        }
    }

    /**
     * Get most recent license by email (active or inactive)
     */
    public function getLicenseByEmail($email) {
        try {
            $sql = "SELECT l.*, au.username as created_by_username
                    FROM licenses l
                    LEFT JOIN admin_users au ON l.created_by = au.id
                    WHERE l.customer_email = :email
                    ORDER BY l.created_at DESC
                    LIMIT 1";

            $stmt = $this->db->prepare($sql);
            $stmt->execute(['email' => $email]);

            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            throw new Exception('Failed to get license by email: ' . $e->getMessage());
        }
    }
    
    /**
     * Update license
     */
    public function updateLicense($id, $data) {
        try {
            // Get existing license
            $existingLicense = $this->getLicenseById($id);
            if (!$existingLicense) {
                throw new Exception('License not found');
            }
            
            // Validate update data
            $this->validateLicenseData($data, true);
            
            // Calculate new expiry date if duration changed
            if (isset($data['duration_months'])) {
                $expiryDate = new DateTime();
                $expiryDate->add(new DateInterval('P' . $data['duration_months'] . 'M'));
                $data['expires_at'] = $expiryDate->format('Y-m-d H:i:s');
            }
            
            // Build update query
            $updateFields = [];
            $params = ['id' => $id];
            
            $allowedFields = ['customer_name', 'customer_email', 'license_type', 'status', 
                            'max_devices', 'duration_months', 'expires_at', 'notes'];
            
            foreach ($allowedFields as $field) {
                if (isset($data[$field])) {
                    $updateFields[] = "$field = :$field";
                    $params[$field] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                throw new Exception('No valid fields to update');
            }
            
            $sql = "UPDATE licenses SET " . implode(', ', $updateFields) . " WHERE id = :id";

            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            
            return $this->getLicenseById($id);
            
        } catch (Exception $e) {
            throw new Exception('Failed to update license: ' . $e->getMessage());
        }
    }
    
    /**
     * Delete license
     */
    public function deleteLicense($id) {
        try {
            $sql = "DELETE FROM licenses WHERE id = :id";
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['id' => $id]);

            return $stmt->rowCount() > 0;

        } catch (Exception $e) {
            throw new Exception('Failed to delete license: ' . $e->getMessage());
        }
    }

    /**
     * Revoke license (mark as revoked instead of deleting)
     */
    public function revokeLicense($id, $reason = null) {
        try {
            $license = $this->getLicenseById($id);
            if (!$license) {
                throw new Exception('License not found');
            }

            // Update license status to revoked
            $updateData = [
                'status' => 'revoked',
                'notes' => ($license['notes'] ? $license['notes'] . ' | ' : '') . 'REVOKED: ' . ($reason ?: 'No reason provided') . ' [' . date('Y-m-d H:i:s') . ']'
            ];

            $this->updateLicense($id, $updateData);

            // Log the revocation
            error_log("License revoked: ID {$id}, Email: {$license['customer_email']}, Reason: " . ($reason ?: 'No reason provided'));

            return true;

        } catch (Exception $e) {
            throw new Exception('Failed to revoke license: ' . $e->getMessage());
        }
    }

    /**
     * Check if a license type is an upgrade from another
     */
    private function isLicenseUpgrade($currentType, $newType) {
        $hierarchy = [
            'trial' => 1,
            'basic' => 2,
            'professional' => 3,
            'enterprise' => 4
        ];

        $currentLevel = $hierarchy[$currentType] ?? 0;
        $newLevel = $hierarchy[$newType] ?? 0;

        return $newLevel > $currentLevel;
    }

    /**
     * Get device count for a license from usage tracking
     */
    public function getDeviceCount($licenseId) {
        try {
            $sql = "SELECT COUNT(DISTINCT device_fingerprint) as device_count
                    FROM license_usage
                    WHERE license_id = :license_id
                    AND device_fingerprint IS NOT NULL
                    AND device_fingerprint != ''";

            $stmt = $this->db->prepare($sql);
            $stmt->execute(['license_id' => $licenseId]);

            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            return $result ? (int)$result['device_count'] : 0;

        } catch (Exception $e) {
            error_log('Failed to get device count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Track device usage for a license
     */
    public function trackDeviceUsage($licenseKey, $deviceFingerprint, $ipAddress, $userAgent = null, $searchQuery = null) {
        try {
            // Get license ID from key
            $license = $this->getLicenseByKey($licenseKey);
            if (!$license) {
                throw new Exception('License not found');
            }

            // Insert usage record
            $sql = "INSERT INTO license_usage (license_id, device_fingerprint, ip_address, user_agent, search_query, created_at)
                    VALUES (:license_id, :device_fingerprint, :ip_address, :user_agent, :search_query, NOW())";

            $stmt = $this->db->prepare($sql);
            $stmt->execute([
                'license_id' => $license['id'],
                'device_fingerprint' => $deviceFingerprint,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent,
                'search_query' => $searchQuery
            ]);

            return true;

        } catch (Exception $e) {
            error_log('Failed to track device usage: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get license by license key
     */
    public function getLicenseByKey($licenseKey) {
        try {
            $sql = "SELECT * FROM licenses WHERE license_key = :license_key";
            $stmt = $this->db->prepare($sql);
            $stmt->execute(['license_key' => $licenseKey]);

            return $stmt->fetch(PDO::FETCH_ASSOC);

        } catch (Exception $e) {
            error_log('Failed to get license by key: ' . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Get license statistics
     */
    public function getLicenseStats() {
        try {
            $stats = [];
            
            // Total licenses
            $sql = "SELECT COUNT(*) as total FROM licenses";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['total'] = $stmt->fetchColumn();

            // Active licenses
            $sql = "SELECT COUNT(*) as active FROM licenses WHERE status = 'active' AND expires_at > NOW()";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['active'] = $stmt->fetchColumn();

            // Expired licenses
            $sql = "SELECT COUNT(*) as expired FROM licenses WHERE expires_at <= NOW()";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['expired'] = $stmt->fetchColumn();

            // Suspended licenses
            $sql = "SELECT COUNT(*) as suspended FROM licenses WHERE status = 'suspended'";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $stats['suspended'] = $stmt->fetchColumn();

            // Revenue calculation (basic estimate)
            $sql = "SELECT license_type, COUNT(*) as count FROM licenses GROUP BY license_type";
            $stmt = $this->db->prepare($sql);
            $stmt->execute();
            $licenseTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $revenue = 0;
            $prices = ['basic' => 29, 'professional' => 79, 'enterprise' => 199];
            
            foreach ($licenseTypes as $type) {
                $revenue += ($prices[$type['license_type']] ?? 0) * $type['count'];
            }
            
            $stats['estimated_revenue'] = $revenue;
            
            return $stats;
            
        } catch (Exception $e) {
            throw new Exception('Failed to get license statistics: ' . $e->getMessage());
        }
    }
    
    /**
     * Generate cryptographic license key
     */
    private function generateLicenseKey($data, $expiryDate) {
        // Add randomness to ensure unique keys
        $randomBytes = bin2hex(random_bytes(16));
        $timestamp = microtime(true);
        $uniqueId = uniqid('', true);

        $licenseData = [
            'type' => $data['license_type'],
            'email' => $data['customer_email'],
            'expiryDate' => $expiryDate->format('Y-m-d'),
            'generated' => date('Y-m-d H:i:s'),
            'random' => $randomBytes,
            'timestamp' => $timestamp,
            'uniqueId' => $uniqueId,
            'version' => '2.0'
        ];

        $encrypted = $this->encryptLicenseData($licenseData);

        // Format as readable license key: XXXX-XXXX-XXXX-XXXX-XXXX-XXXX (use full encrypted data)
        $chunks = str_split($encrypted, 4);
        return strtoupper(implode('-', $chunks));
    }
    
    /**
     * Encrypt license data (compatible with JavaScript XOR encryption)
     */
    private function encryptLicenseData($data) {
        $jsonStr = json_encode($data);
        $secret = $this->licenseSecret;
        $encrypted = '';

        for ($i = 0; $i < strlen($jsonStr); $i++) {
            $encrypted .= chr(ord($jsonStr[$i]) ^ ord($secret[$i % strlen($secret)]));
        }

        // Convert to hex
        $hex = '';
        for ($i = 0; $i < strlen($encrypted); $i++) {
            $hex .= str_pad(dechex(ord($encrypted[$i])), 2, '0', STR_PAD_LEFT);
        }

        return strtoupper($hex);
    }
    
    /**
     * Validate license data
     */
    private function validateLicenseData($data, $isUpdate = false) {
        if (!$isUpdate) {
            if (empty($data['customer_name'])) {
                throw new Exception('Customer name is required');
            }
            
            if (empty($data['customer_email'])) {
                throw new Exception('Customer email is required');
            }
            
            if (!filter_var($data['customer_email'], FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid email address');
            }
            
            if (empty($data['license_type']) || !in_array($data['license_type'], ['basic', 'professional', 'enterprise'])) {
                throw new Exception('Invalid license type');
            }
            
            if (empty($data['duration_months']) || !is_numeric($data['duration_months']) || $data['duration_months'] < 1) {
                throw new Exception('Invalid duration');
            }
        }
        
        // Validation for updates
        if (isset($data['customer_email']) && !filter_var($data['customer_email'], FILTER_VALIDATE_EMAIL)) {
            throw new Exception('Invalid email address');
        }
        
        if (isset($data['license_type']) && !in_array($data['license_type'], ['basic', 'professional', 'enterprise'])) {
            throw new Exception('Invalid license type');
        }
        
        if (isset($data['status']) && !in_array($data['status'], ['active', 'suspended', 'expired', 'revoked', 'superseded'])) {
            throw new Exception('Invalid status');
        }
        
        if (isset($data['duration_months']) && (!is_numeric($data['duration_months']) || $data['duration_months'] < 1)) {
            throw new Exception('Invalid duration');
        }
    }
}
